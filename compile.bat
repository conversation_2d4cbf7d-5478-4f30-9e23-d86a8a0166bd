@echo off
echo Compiling Process Proxy Tool...
echo.

echo [1/3] Compiling with gcc...
gcc -Wall -Wextra -O2 -std=c99 -I"WinDivert-2.2.2-A\include" -o process-proxy.exe main.c "WinDivert-2.2.2-A\x64\WinDivert.lib" -lws2_32

if %errorLevel% == 0 (
    echo [OK] Compilation successful!
) else (
    echo [ERROR] Compilation failed!
    pause
    exit /b 1
)

echo.
echo [2/3] Copying runtime files...
copy "WinDivert-2.2.2-A\x64\WinDivert.dll" "." >nul
copy "WinDivert-2.2.2-A\x64\WinDivert64.sys" "." >nul
echo [OK] Runtime files copied!

echo.
echo [3/3] Testing executable...
process-proxy.exe -h
echo.
echo [OK] Build complete! Use 'process-proxy.exe -h' for help.
pause
