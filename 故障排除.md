# Process Proxy Tool 故障排除指南

## 常见错误及解决方案

### 错误代码 87 - 参数无效

**错误信息：**
```
Error: failed to open WinDivert device (error code: 87)
```

**原因分析：**
1. **过滤器语法错误** - 最常见原因
2. **缺少管理员权限**
3. **进程名称不正确**

**解决方案：**

#### 1. 检查过滤器语法
❌ **错误写法：**
```bash
process-proxy.exe -f "tcp && ProcessName==firefox.exe"  # 缺少空格
```

✅ **正确写法：**
```bash
process-proxy.exe -f "tcp && ProcessName == firefox.exe"  # 注意 == 前后的空格
```

#### 2. 以管理员身份运行
```bash
# 使用提供的管理员脚本
run-as-admin.bat

# 或者使用Firefox专用脚本
firefox-proxy.bat
```

#### 3. 验证进程名称
使用任务管理器确认进程的确切名称：
- Firefox: `firefox.exe`
- Chrome: `chrome.exe`
- Edge: `msedge.exe`

### 错误代码 5 - 访问被拒绝

**错误信息：**
```
Error: failed to open WinDivert device (error code: 5)
```

**解决方案：**
1. **以管理员身份运行**
2. **检查杀毒软件** - 可能阻止了WinDivert驱动
3. **检查Windows Defender** - 添加程序到白名单

### 错误代码 2 - 系统找不到指定的文件

**错误信息：**
```
Error: failed to open WinDivert device (error code: 2)
```

**解决方案：**
1. **检查WinDivert64.sys文件**
   ```bash
   # 确保文件存在
   dir WinDivert64.sys
   ```

2. **重新复制运行时文件**
   ```bash
   copy "WinDivert-2.2.2-A\x64\WinDivert.dll" "."
   copy "WinDivert-2.2.2-A\x64\WinDivert64.sys" "."
   ```

### 没有拦截到数据包

**现象：**
程序运行正常，但没有显示任何数据包信息

**可能原因：**
1. **目标进程未运行**
2. **进程名称错误**
3. **目标进程没有网络活动**
4. **过滤器过于严格**

**解决方案：**

#### 1. 确认目标进程正在运行
```bash
# 使用任务管理器或命令行检查
tasklist | findstr firefox.exe
```

#### 2. 使用详细模式调试
```bash
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
```

#### 3. 测试更宽泛的过滤器
```bash
# 测试所有TCP流量（谨慎使用，会产生大量输出）
process-proxy.exe -v -f "tcp"
```

#### 4. 确保目标进程有网络活动
- 在浏览器中访问网站
- 确保访问的是HTTP/HTTPS网站（端口80/443）

### mitmproxy连接问题

**现象：**
代理工具显示重定向成功，但mitmproxy没有收到请求

**解决方案：**

#### 1. 确认mitmproxy正在运行
```bash
# 启动mitmproxy
mitmproxy -p 8888

# 或使用Web界面
mitmweb -p 8888
```

#### 2. 检查端口是否被占用
```bash
netstat -an | findstr :8888
```

#### 3. 检查防火墙设置
- 确保端口8888未被防火墙阻止
- 临时关闭防火墙测试

### 性能问题

**现象：**
程序运行缓慢或占用大量CPU

**解决方案：**

#### 1. 关闭详细输出
```bash
# 不使用 -v 参数
process-proxy.exe -p 8888 -f "tcp && ProcessName == firefox.exe"
```

#### 2. 使用更精确的过滤器
```bash
# 只拦截特定端口
process-proxy.exe -f "tcp && ProcessName == firefox.exe && (tcp.DstPort == 80 || tcp.DstPort == 443)"
```

## 调试技巧

### 1. 逐步调试

**步骤1：测试基本功能**
```bash
process-proxy.exe -h
```

**步骤2：测试权限**
```bash
process-proxy.exe -v -f "tcp && ProcessName == notepad.exe"
```

**步骤3：测试实际目标**
```bash
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
```

### 2. 使用日志文件

**重定向输出到文件：**
```bash
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe" > proxy.log 2>&1
```

### 3. 网络测试

**使用curl测试连接：**
```bash
# 测试代理端口是否可访问
curl -x localhost:8888 http://httpbin.org/ip
```

## 常用命令参考

### Firefox代理
```bash
# 基本模式
process-proxy.exe -p 8888 -f "tcp && ProcessName == firefox.exe"

# 详细模式
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
```

### Chrome代理
```bash
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == chrome.exe"
```

### Edge代理
```bash
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == msedge.exe"
```

### 自定义进程
```bash
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == your-app.exe"
```

## 系统要求检查清单

- [ ] Windows 7/8/10/11 (64位)
- [ ] 管理员权限
- [ ] gcc编译器已安装
- [ ] WinDivert库文件完整
- [ ] 杀毒软件白名单设置
- [ ] 防火墙端口开放
- [ ] 目标进程正在运行

## 获取帮助

如果以上方法都无法解决问题，请：

1. **收集信息：**
   - 错误代码和完整错误信息
   - 使用的命令行参数
   - 目标进程名称
   - Windows版本

2. **生成详细日志：**
   ```bash
   process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe" > debug.log 2>&1
   ```

3. **检查系统环境：**
   ```bash
   # 检查WinDivert文件
   dir WinDivert*
   
   # 检查进程
   tasklist | findstr firefox
   
   # 检查端口
   netstat -an | findstr :8888
   ```
