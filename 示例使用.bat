@echo off
chcp 65001 >nul

echo ========================================
echo Process Proxy Tool 使用示例
echo ========================================
echo.

echo 本脚本将演示如何正确使用Process Proxy Tool
echo.

echo 重要提醒:
echo 1. 必须以管理员身份运行
echo 2. 过滤器语法中的 == 前后必须有空格
echo 3. 确保目标进程正在运行
echo 4. 确保mitmproxy在指定端口运行
echo.

pause

echo ========================================
echo 示例1: 显示帮助信息
echo ========================================
echo.
echo 命令: process-proxy.exe -h
echo.
process-proxy.exe -h
echo.
pause

echo ========================================
echo 示例2: 错误的过滤器语法 (会失败)
echo ========================================
echo.
echo 命令: process-proxy.exe -p 8888 -f "tcp && ProcessName==firefox.exe"
echo 注意: ProcessName==firefox.exe 缺少空格，会导致错误87
echo.
process-proxy.exe -p 8888 -f "tcp && ProcessName==firefox.exe"
echo.
echo 如您所见，这会导致参数无效错误
pause

echo ========================================
echo 示例3: 正确的过滤器语法
echo ========================================
echo.
echo 命令: process-proxy.exe -p 8888 -f "tcp && ProcessName == firefox.exe"
echo 注意: ProcessName == firefox.exe 有正确的空格
echo.
echo 这个命令需要:
echo 1. 管理员权限
echo 2. Firefox浏览器正在运行
echo 3. mitmproxy在端口8888运行
echo.
echo 如果您想测试，请:
echo 1. 以管理员身份打开新的命令提示符
echo 2. 启动Firefox浏览器
echo 3. 在另一个终端运行: mitmproxy -p 8888
echo 4. 然后运行上述命令
echo.
pause

echo ========================================
echo 示例4: 详细输出模式
echo ========================================
echo.
echo 命令: process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
echo.
echo 参数说明:
echo -v : 启用详细输出，显示每个数据包的详细信息
echo -s : 启用统计信息，每10秒显示一次统计
echo -p 8888 : 设置代理端口为8888
echo -f "..." : 设置过滤器，只拦截Firefox的TCP流量
echo.
pause

echo ========================================
echo 示例5: 不同浏览器的正确命令
echo ========================================
echo.
echo Firefox:
echo process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
echo.
echo Chrome:
echo process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == chrome.exe"
echo.
echo Edge:
echo process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == msedge.exe"
echo.
echo Internet Explorer:
echo process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == iexplore.exe"
echo.
pause

echo ========================================
echo 示例6: 完整的测试流程
echo ========================================
echo.
echo 完整测试步骤:
echo.
echo 1. 编译程序:
echo    compile.bat
echo.
echo 2. 启动mitmproxy:
echo    mitmproxy -p 8888
echo.
echo 3. 以管理员身份运行代理工具:
echo    run-as-admin.bat
echo    或
echo    firefox-proxy.bat
echo.
echo 4. 启动Firefox并访问网站:
echo    - 打开Firefox
echo    - 访问 http://httpbin.org/ip
echo    - 观察代理工具和mitmproxy的输出
echo.
echo 5. 验证拦截成功:
echo    - 代理工具应显示 "REDIRECTED" 消息
echo    - mitmproxy应显示拦截的HTTP请求
echo.
pause

echo ========================================
echo 故障排除提示
echo ========================================
echo.
echo 如果遇到问题，请检查:
echo.
echo 错误代码87 (参数无效):
echo - 检查过滤器语法，确保 == 前后有空格
echo - 确认进程名称正确
echo.
echo 错误代码5 (访问被拒绝):
echo - 以管理员身份运行
echo - 检查杀毒软件是否阻止
echo.
echo 错误代码2 (文件未找到):
echo - 确保WinDivert64.sys文件存在
echo - 重新复制运行时文件
echo.
echo 没有拦截到数据包:
echo - 确认目标进程正在运行
echo - 确认进程有网络活动
echo - 使用 -v 参数查看详细信息
echo.
echo 详细故障排除指南请参考: 故障排除.md
echo.

echo ========================================
echo 示例演示完成
echo ========================================
echo.
echo 现在您可以:
echo 1. 运行 run-as-admin.bat 开始使用
echo 2. 运行 firefox-proxy.bat 快速测试Firefox
echo 3. 查看 故障排除.md 解决问题
echo 4. 查看 使用说明.md 了解更多详情
echo.
pause
