@echo off

echo ========================================
echo Process Proxy Tool - New Version Test
echo ========================================
echo.

echo Checking admin privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running as administrator
) else (
    echo [ERROR] Need administrator privileges
    echo Please run as administrator
    pause
    exit /b 1
)

echo.
echo [INFO] This new version intercepts ALL HTTP traffic, not just specific processes
echo [INFO] It will redirect all outbound HTTP traffic to the proxy port
echo.

echo Configuration:
echo - Filter: outbound and tcp.DstPort == 80
echo - Proxy Port: 8888
echo - Target: ALL processes (not filtered)
echo.

echo Instructions:
echo 1. Make sure mitmproxy is running: mitmproxy -p 8888
echo 2. Open ANY browser (Firefox, Chrome, Edge, etc.)
echo 3. Visit any HTTP website (not HTTPS)
echo 4. Watch the output below
echo 5. Press Ctrl+C to stop
echo.

echo Starting proxy tool...
echo ========================================
echo.

process-proxy.exe -v -s -p 8888

echo.
echo Proxy tool stopped
pause
