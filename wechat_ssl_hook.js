// 微信SSL/TLS流量解密Hook脚本
// 专门用于捕获和解密HTTPS流量
// 使用方法: frida -p <WeChat进程ID> -l wechat_ssl_hook.js

console.log("[+] 微信SSL/TLS流量解密脚本已启动");

// 配置
var CONFIG = {
    dumpSSLKeys: true,
    saveToFile: true,
    maxDataSize: 2048,
    logLevel: "INFO"
};

// SSL会话存储
var sslSessions = new Map();
var sessionCounter = 0;

// 日志函数
function logSSL(level, message, data) {
    var timestamp = new Date().toISOString();
    console.log(`[${timestamp}][${level}] ${message}`);
    if (data) {
        console.log(JSON.stringify(data, null, 2));
    }
}

// 数据转换函数
function bufferToHex(buffer, length) {
    var result = "";
    var data = new Uint8Array(buffer);
    for (var i = 0; i < length && i < data.length; i++) {
        var hex = data[i].toString(16);
        result += (hex.length === 1 ? "0" + hex : hex);
    }
    return result;
}

function bufferToString(buffer, length) {
    var result = "";
    var data = new Uint8Array(buffer);
    for (var i = 0; i < length && i < data.length; i++) {
        var char = data[i];
        if (char >= 32 && char <= 126) {
            result += String.fromCharCode(char);
        } else {
            result += ".";
        }
    }
    return result;
}

// 检测HTTP内容
function isHTTPContent(data) {
    var str = bufferToString(data, Math.min(100, data.length));
    return str.includes("HTTP/") || str.includes("GET ") || str.includes("POST ") || 
           str.includes("Content-Type") || str.includes("User-Agent");
}

// 解析HTTP请求/响应
function parseHTTPContent(data, length) {
    try {
        var content = bufferToString(data, length);
        var lines = content.split('\n');
        
        var result = {
            type: "unknown",
            method: "",
            url: "",
            status: "",
            headers: {},
            body: ""
        };
        
        if (lines.length > 0) {
            var firstLine = lines[0].trim();
            if (firstLine.startsWith("HTTP/")) {
                // HTTP响应
                result.type = "response";
                var parts = firstLine.split(' ');
                if (parts.length >= 2) {
                    result.status = parts[1];
                }
            } else if (firstLine.includes(" HTTP/")) {
                // HTTP请求
                result.type = "request";
                var parts = firstLine.split(' ');
                if (parts.length >= 2) {
                    result.method = parts[0];
                    result.url = parts[1];
                }
            }
        }
        
        // 解析头部
        var headerEnd = false;
        var bodyStart = 0;
        for (var i = 1; i < lines.length; i++) {
            if (lines[i].trim() === "" && !headerEnd) {
                headerEnd = true;
                bodyStart = i + 1;
                break;
            }
            
            if (!headerEnd && lines[i].includes(":")) {
                var colonIndex = lines[i].indexOf(":");
                var key = lines[i].substring(0, colonIndex).trim();
                var value = lines[i].substring(colonIndex + 1).trim();
                result.headers[key] = value;
            }
        }
        
        // 提取body
        if (bodyStart < lines.length) {
            result.body = lines.slice(bodyStart).join('\n').trim();
        }
        
        return result;
    } catch (e) {
        return {error: e.toString()};
    }
}

// Hook SSL_write函数
function hookSSLWrite() {
    var sslLibraries = [
        "ssleay32.dll", 
        "libssl-1_1.dll", 
        "libssl-3.dll",
        "libssl.dll",
        "libeay32.dll"
    ];
    
    sslLibraries.forEach(function(libName) {
        try {
            var sslWritePtr = Module.findExportByName(libName, "SSL_write");
            if (sslWritePtr) {
                Interceptor.attach(sslWritePtr, {
                    onEnter: function(args) {
                        this.ssl = args[0];
                        this.buf = args[1];
                        this.num = args[2].toInt32();
                        this.sessionId = this.ssl.toString();
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() > 0 && this.num > 0) {
                            try {
                                var data = Memory.readByteArray(this.buf, Math.min(this.num, CONFIG.maxDataSize));
                                var dataArray = new Uint8Array(data);
                                
                                var logData = {
                                    session: this.sessionId,
                                    direction: "SEND",
                                    length: this.num,
                                    written: retval.toInt32(),
                                    timestamp: Date.now()
                                };
                                
                                // 检查是否为HTTP内容
                                if (isHTTPContent(dataArray)) {
                                    logData.type = "HTTP";
                                    logData.http = parseHTTPContent(dataArray, dataArray.length);
                                    logData.preview = bufferToString(dataArray, Math.min(200, dataArray.length));
                                } else {
                                    logData.type = "BINARY";
                                    logData.hex = bufferToHex(dataArray, Math.min(64, dataArray.length));
                                    logData.preview = bufferToString(dataArray, Math.min(100, dataArray.length));
                                }
                                
                                logSSL("INFO", "SSL发送数据", logData);
                                
                                // 保存会话信息
                                if (!sslSessions.has(this.sessionId)) {
                                    sslSessions.set(this.sessionId, {
                                        id: ++sessionCounter,
                                        created: Date.now(),
                                        sent: 0,
                                        received: 0
                                    });
                                }
                                sslSessions.get(this.sessionId).sent += retval.toInt32();
                                
                            } catch (e) {
                                logSSL("ERROR", "SSL_write hook错误", {error: e.toString()});
                            }
                        }
                    }
                });
                logSSL("INFO", `已hook SSL_write from ${libName}`);
                return true;
            }
        } catch (e) {
            // 库不存在，继续尝试
        }
    });
    return false;
}

// Hook SSL_read函数
function hookSSLRead() {
    var sslLibraries = [
        "ssleay32.dll", 
        "libssl-1_1.dll", 
        "libssl-3.dll",
        "libssl.dll",
        "libeay32.dll"
    ];
    
    sslLibraries.forEach(function(libName) {
        try {
            var sslReadPtr = Module.findExportByName(libName, "SSL_read");
            if (sslReadPtr) {
                Interceptor.attach(sslReadPtr, {
                    onEnter: function(args) {
                        this.ssl = args[0];
                        this.buf = args[1];
                        this.num = args[2].toInt32();
                        this.sessionId = this.ssl.toString();
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() > 0) {
                            try {
                                var data = Memory.readByteArray(this.buf, retval.toInt32());
                                var dataArray = new Uint8Array(data);
                                
                                var logData = {
                                    session: this.sessionId,
                                    direction: "RECV",
                                    length: retval.toInt32(),
                                    timestamp: Date.now()
                                };
                                
                                // 检查是否为HTTP内容
                                if (isHTTPContent(dataArray)) {
                                    logData.type = "HTTP";
                                    logData.http = parseHTTPContent(dataArray, dataArray.length);
                                    logData.preview = bufferToString(dataArray, Math.min(200, dataArray.length));
                                } else {
                                    logData.type = "BINARY";
                                    logData.hex = bufferToHex(dataArray, Math.min(64, dataArray.length));
                                    logData.preview = bufferToString(dataArray, Math.min(100, dataArray.length));
                                }
                                
                                logSSL("INFO", "SSL接收数据", logData);
                                
                                // 更新会话信息
                                if (!sslSessions.has(this.sessionId)) {
                                    sslSessions.set(this.sessionId, {
                                        id: ++sessionCounter,
                                        created: Date.now(),
                                        sent: 0,
                                        received: 0
                                    });
                                }
                                sslSessions.get(this.sessionId).received += retval.toInt32();
                                
                            } catch (e) {
                                logSSL("ERROR", "SSL_read hook错误", {error: e.toString()});
                            }
                        }
                    }
                });
                logSSL("INFO", `已hook SSL_read from ${libName}`);
                return true;
            }
        } catch (e) {
            // 库不存在，继续尝试
        }
    });
    return false;
}

// Hook SSL连接建立
function hookSSLConnect() {
    var sslLibraries = ["ssleay32.dll", "libssl-1_1.dll", "libssl-3.dll"];
    
    sslLibraries.forEach(function(libName) {
        try {
            var sslConnectPtr = Module.findExportByName(libName, "SSL_connect");
            if (sslConnectPtr) {
                Interceptor.attach(sslConnectPtr, {
                    onEnter: function(args) {
                        this.ssl = args[0];
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() === 1) {
                            logSSL("INFO", "SSL连接建立", {
                                session: this.ssl.toString(),
                                result: "success"
                            });
                        }
                    }
                });
                logSSL("INFO", `已hook SSL_connect from ${libName}`);
            }
        } catch (e) {
            // 库不存在，继续尝试
        }
    });
}

// 主函数
function main() {
    try {
        logSSL("INFO", "开始设置SSL/TLS hooks...");
        
        var writeHooked = hookSSLWrite();
        var readHooked = hookSSLRead();
        hookSSLConnect();
        
        if (!writeHooked && !readHooked) {
            logSSL("WARN", "未找到SSL库，可能微信使用了不同的SSL实现");
        }
        
        // 定期输出会话统计
        setInterval(function() {
            if (sslSessions.size > 0) {
                logSSL("INFO", "SSL会话统计", {
                    total_sessions: sslSessions.size,
                    sessions: Array.from(sslSessions.entries()).map(function([key, value]) {
                        return {
                            session_id: value.id,
                            sent_bytes: value.sent,
                            received_bytes: value.received,
                            duration: Date.now() - value.created
                        };
                    })
                });
            }
        }, 30000);
        
        logSSL("INFO", "SSL/TLS流量监控已启动");
        
    } catch (e) {
        logSSL("ERROR", "初始化失败", {error: e.toString()});
    }
}

// 启动
main();
