# 系统代理设置 PowerShell 脚本
# 用法: Set-Proxy.ps1 -Enable 或 Set-Proxy.ps1 -Disable

param(
    [switch]$Enable,
    [switch]$Disable,
    [string]$ProxyServer = "localhost:8888"
)

function Set-SystemProxy {
    param(
        [bool]$EnableProxy,
        [string]$Server = "localhost:8888"
    )
    
    $regPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings"
    
    if ($EnableProxy) {
        Write-Host "启用系统代理: $Server" -ForegroundColor Green
        
        # 启用代理
        Set-ItemProperty -Path $regPath -Name ProxyEnable -Value 1
        Set-ItemProperty -Path $regPath -Name ProxyServer -Value $Server
        
        # 设置代理绕过列表
        $bypass = "localhost;127.*;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*;<local>"
        Set-ItemProperty -Path $regPath -Name ProxyOverride -Value $bypass
        
        Write-Host "✓ 代理已启用" -ForegroundColor Green
        
    } else {
        Write-Host "禁用系统代理" -ForegroundColor Yellow
        
        # 禁用代理
        Set-ItemProperty -Path $regPath -Name ProxyEnable -Value 0
        
        Write-Host "✓ 代理已禁用" -ForegroundColor Green
    }
    
    # 通知系统设置已更改
    $signature = @'
[DllImport("wininet.dll")]
public static extern bool InternetSetOption(IntPtr hInternet, int dwOption, IntPtr lpBuffer, int dwBufferLength);
'@
    
    try {
        $wininet = Add-Type -MemberDefinition $signature -Name WinINet -Namespace InternetSettings -PassThru
        $wininet::InternetSetOption([IntPtr]::Zero, 39, [IntPtr]::Zero, 0) | Out-Null
        $wininet::InternetSetOption([IntPtr]::Zero, 37, [IntPtr]::Zero, 0) | Out-Null
        Write-Host "✓ 系统已通知代理设置更改" -ForegroundColor Green
    } catch {
        Write-Host "! 无法通知系统，请手动重启浏览器" -ForegroundColor Yellow
    }
}

function Get-CurrentProxy {
    $regPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Internet Settings"
    
    try {
        $proxyEnabled = Get-ItemProperty -Path $regPath -Name ProxyEnable -ErrorAction SilentlyContinue
        $proxyServer = Get-ItemProperty -Path $regPath -Name ProxyServer -ErrorAction SilentlyContinue
        
        Write-Host "当前代理设置:" -ForegroundColor Cyan
        Write-Host "----------------------------------------"
        
        if ($proxyEnabled.ProxyEnable -eq 1) {
            Write-Host "状态: 启用" -ForegroundColor Green
            Write-Host "服务器: $($proxyServer.ProxyServer)" -ForegroundColor Green
        } else {
            Write-Host "状态: 禁用" -ForegroundColor Yellow
        }
    } catch {
        Write-Host "无法读取代理设置" -ForegroundColor Red
    }
}

# 主逻辑
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "系统代理设置工具" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

if ($Enable) {
    Set-SystemProxy -EnableProxy $true -Server $ProxyServer
} elseif ($Disable) {
    Set-SystemProxy -EnableProxy $false
} else {
    # 交互式菜单
    Get-CurrentProxy
    Write-Host ""
    Write-Host "请选择操作:"
    Write-Host "1. 启用代理 ($ProxyServer)"
    Write-Host "2. 禁用代理"
    Write-Host "3. 查看当前设置"
    Write-Host "4. 退出"
    Write-Host ""
    
    $choice = Read-Host "请选择 (1-4)"
    
    switch ($choice) {
        "1" {
            Set-SystemProxy -EnableProxy $true -Server $ProxyServer
        }
        "2" {
            Set-SystemProxy -EnableProxy $false
        }
        "3" {
            Get-CurrentProxy
        }
        "4" {
            Write-Host "退出程序" -ForegroundColor Yellow
            exit
        }
        default {
            Write-Host "无效选择" -ForegroundColor Red
            exit 1
        }
    }
}

Write-Host ""
Write-Host "重要提醒:" -ForegroundColor Yellow
Write-Host "1. 确保 mitmproxy 在端口 8888 运行"
Write-Host "2. 重启浏览器以使设置生效"
Write-Host "3. 使用完毕后记得禁用代理"
Write-Host ""

Read-Host "按 Enter 键退出"
