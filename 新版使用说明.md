# Process Proxy Tool v1.0 - 更新版使用说明

## 🔄 **重要更新**

由于WinDivert在网络层不直接支持进程名过滤，我们已经更新了程序架构：

- ✅ **新方法**：使用端口过滤 + 软件层进程检查
- ✅ **更稳定**：避免了WinDivert过滤器语法问题
- ✅ **更灵活**：支持自定义端口和进程

## 🚀 **新的使用方法**

### 基本命令

```bash
# 显示帮助
process-proxy.exe -h

# Firefox代理（默认）
process-proxy.exe -v -s -p 8888

# Chrome代理
process-proxy.exe -v -s -p 8888 -t chrome.exe

# Edge代理
process-proxy.exe -v -s -p 8888 -t msedge.exe

# 自定义进程
process-proxy.exe -v -s -p 8888 -t your-app.exe
```

### 新参数说明

| 参数 | 长参数 | 说明 | 默认值 |
|------|--------|------|--------|
| `-h` | `--help` | 显示帮助信息 | - |
| `-p` | `--port` | 代理端口 | 8080 |
| `-t` | `--target` | 目标进程名 | firefox.exe |
| `-f` | `--filter` | WinDivert过滤器 | `tcp.DstPort == 80 or tcp.DstPort == 443` |
| `-v` | `--verbose` | 详细输出 | 关闭 |
| `-s` | `--stats` | 统计信息 | 关闭 |

## 🔧 **工作原理**

1. **端口过滤**：WinDivert拦截所有HTTP(80)/HTTPS(443)流量
2. **软件过滤**：程序检查数据包是否来自目标进程
3. **流量重定向**：将匹配的流量重定向到代理端口

## 📋 **测试步骤**

### 方法1：快速测试
```bash
# 1. 以管理员身份运行
test-simple.bat
```

### 方法2：完整测试
```bash
# 1. 启动mitmproxy
mitmproxy -p 8888

# 2. 以管理员身份运行代理工具
process-proxy.exe -v -s -p 8888 -t firefox.exe

# 3. 在Firefox中访问网站
# 访问 http://httpbin.org/ip
```

## 🎯 **推荐命令**

### Firefox
```bash
process-proxy.exe -v -s -p 8888 -t firefox.exe
```

### Chrome
```bash
process-proxy.exe -v -s -p 8888 -t chrome.exe
```

### Edge
```bash
process-proxy.exe -v -s -p 8888 -t msedge.exe
```

### 只拦截HTTP（端口80）
```bash
process-proxy.exe -v -s -p 8888 -t firefox.exe -f "tcp.DstPort == 80"
```

### 只拦截HTTPS（端口443）
```bash
process-proxy.exe -v -s -p 8888 -t firefox.exe -f "tcp.DstPort == 443"
```

## 🔍 **故障排除**

### 错误代码5（访问被拒绝）
```
解决方案：
1. 以管理员身份运行命令提示符
2. 检查杀毒软件是否阻止WinDivert
3. 确保WinDivert64.sys文件存在
```

### 错误代码87（参数无效）
```
解决方案：
1. 检查过滤器语法
2. 使用默认过滤器测试
3. 确认WinDivert版本兼容
```

### 没有拦截到数据包
```
解决方案：
1. 确认目标进程正在运行
2. 确认进程有网络活动
3. 使用 -v 参数查看详细信息
4. 尝试访问HTTP网站（不是HTTPS）
```

## 📊 **成功运行示例**

```
[2025-07-10 12:26:37][main:108] Process Proxy Tool v1.0 - Starting initialization...
[2025-07-10 12:26:37][main:187] Running with administrator privileges.
[2025-07-10 12:26:37][main:193] Opening WinDivert device with filter: tcp.DstPort == 80 or tcp.DstPort == 443
[2025-07-10 12:26:37][main:194] Target process: firefox.exe (process filtering will be done in software)
[2025-07-10 12:26:37][main:206] WinDivert device opened successfully.
[2025-07-10 12:26:37][main:215] Entering main packet processing loop...
[2025-07-10 12:26:40][VERBOSE][main:235] Received packet #1 (length: 60 bytes)
[2025-07-10 12:26:40][VERBOSE][main:257] Redirecting HTTP traffic from port 80 to proxy port 8888
```

## 🎮 **测试脚本**

我们提供了几个测试脚本：

1. **`test-simple.bat`** - 基本功能测试
2. **`test-firefox.bat`** - Firefox专用测试
3. **`start-firefox.bat`** - 带权限提升的启动脚本

## 🔄 **从旧版本迁移**

如果您之前使用的是包含`ProcessName`的过滤器：

```bash
# 旧版本（不工作）
process-proxy.exe -f "tcp && ProcessName == firefox.exe"

# 新版本（工作）
process-proxy.exe -t firefox.exe
```

## 💡 **高级用法**

### 自定义端口范围
```bash
process-proxy.exe -v -t firefox.exe -f "tcp.DstPort >= 80 and tcp.DstPort <= 8080"
```

### 特定IP地址
```bash
process-proxy.exe -v -t firefox.exe -f "tcp.DstPort == 80 and ip.DstAddr == *************"
```

### 调试模式（拦截所有TCP）
```bash
# 警告：会产生大量输出
process-proxy.exe -v -f "tcp" -t firefox.exe
```

## 📝 **注意事项**

1. **必须以管理员身份运行**
2. **确保目标进程正在运行**
3. **确保mitmproxy在指定端口运行**
4. **首次运行可能需要防火墙授权**
5. **杀毒软件可能需要添加白名单**

## 🆘 **获取支持**

如果遇到问题：

1. 查看 `故障排除.md`
2. 运行 `test-simple.bat` 进行基本测试
3. 使用 `-v` 参数获取详细日志
4. 检查Windows事件查看器中的错误信息
