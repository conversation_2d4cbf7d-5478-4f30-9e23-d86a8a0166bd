# 代理不工作问题诊断

## 🔍 **问题分析**

根据您的输出，问题是：

```
[2025-07-10 12:31:19][main:187] Warning: Not running as administrator. WinDivert may fail to open.
[2025-07-10 12:31:19][main:198] Error: failed to open WinDivert device (error code: 5)
```

**错误代码5 = 访问被拒绝**，这意味着程序没有足够的权限打开WinDivert设备。

## ⚠️ **根本原因**

WinDivert需要**管理员权限**才能拦截网络流量。如果没有管理员权限：
- WinDivert设备无法打开
- 无法拦截任何数据包
- 无法重定向流量到代理

## ✅ **解决方案**

### 方法1：手动以管理员身份运行

1. **关闭当前命令提示符**
2. **以管理员身份打开新的命令提示符**：
   - 按 `Win + R`
   - 输入 `cmd`
   - 按 `Ctrl + Shift + Enter`（这会以管理员身份运行）
   - 点击"是"确认UAC提示

3. **导航到程序目录**：
   ```cmd
   cd "e:\test\test_mitmproxy_frida\强制进程走代理"
   ```

4. **运行程序**：
   ```cmd
   process-proxy.exe -v -s -p 8888 -t firefox.exe
   ```

### 方法2：使用PowerShell自动提升权限

创建一个PowerShell脚本自动请求管理员权限：

```powershell
# 检查是否为管理员
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator"))
{
    # 重新以管理员身份启动
    Start-Process PowerShell -Verb RunAs -ArgumentList "-NoProfile -ExecutionPolicy Bypass -Command `"cd '$PWD'; .\process-proxy.exe -v -s -p 8888 -t firefox.exe`""
    exit
}

# 如果已经是管理员，直接运行
.\process-proxy.exe -v -s -p 8888 -t firefox.exe
```

### 方法3：使用批处理文件自动提升

```batch
@echo off
:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running as administrator
    goto :run_program
) else (
    echo [INFO] Requesting administrator privileges...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
    exit /b
)

:run_program
cd /d "%~dp0"
echo Starting proxy with admin privileges...
process-proxy.exe -v -s -p 8888 -t firefox.exe
pause
```

## 🧪 **验证步骤**

成功以管理员身份运行后，您应该看到：

```
[2025-07-10 12:31:19][main:175] Running with administrator privileges.
[2025-07-10 12:31:19][main:206] WinDivert device opened successfully.
[2025-07-10 12:31:19][main:215] Entering main packet processing loop...
```

而不是：
```
[2025-07-10 12:31:19][main:187] Warning: Not running as administrator.
[2025-07-10 12:31:19][main:198] Error: failed to open WinDivert device (error code: 5)
```

## 🎯 **完整测试流程**

1. **启动mitmproxy**（在普通命令提示符中）：
   ```cmd
   mitmproxy -p 8888
   ```

2. **以管理员身份启动代理工具**：
   ```cmd
   # 在管理员命令提示符中
   process-proxy.exe -v -s -p 8888 -t firefox.exe
   ```

3. **在Firefox中测试**：
   - 访问 http://httpbin.org/ip
   - 观察代理工具输出是否显示拦截的数据包
   - 检查mitmproxy是否收到请求

## 🔧 **预期的成功输出**

```
[2025-07-10 12:31:19][main:108] Process Proxy Tool v1.0 - Starting initialization...
[2025-07-10 12:31:19][main:175] Running with administrator privileges.
[2025-07-10 12:31:19][main:206] WinDivert device opened successfully.
[2025-07-10 12:31:19][main:215] Entering main packet processing loop...
[2025-07-10 12:31:19][main:216] Monitoring traffic and redirecting HTTP(80)/HTTPS(443) to proxy port 8888

# 当Firefox访问网站时，您会看到：
[2025-07-10 12:31:25][VERBOSE][main:235] Received packet #1 (length: 60 bytes)
[2025-07-10 12:31:25][VERBOSE][main:257] Redirecting HTTP traffic from port 80 to proxy port 8888
[2025-07-10 12:31:25][VERBOSE][print_packet_info:58] Packet #1 [REDIRECTED]: *************:54321 -> *************:8888 (len=60, proto=6)
```

## 🚨 **常见错误**

### 错误1：忘记以管理员身份运行
```
症状：Error code 5, "Not running as administrator"
解决：重新以管理员身份打开命令提示符
```

### 错误2：mitmproxy未运行
```
症状：代理工具显示重定向，但连接失败
解决：确保 mitmproxy -p 8888 正在运行
```

### 错误3：Firefox未运行
```
症状：没有拦截到任何数据包
解决：启动Firefox并访问HTTP网站
```

## 💡 **快速解决**

最快的解决方法：

1. **右键点击"命令提示符"图标**
2. **选择"以管理员身份运行"**
3. **运行命令**：
   ```cmd
   cd "e:\test\test_mitmproxy_frida\强制进程走代理"
   process-proxy.exe -v -s -p 8888 -t firefox.exe
   ```

这样就能解决权限问题，程序应该能正常拦截和重定向流量了。
