@echo off

echo Firefox Proxy Test
echo ==================

echo Checking admin privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running as administrator
) else (
    echo [ERROR] Need administrator privileges
    echo Please run as administrator
    pause
    exit /b 1
)

echo.
echo Starting Firefox proxy on port 8888...
echo Make sure Firefox is running and mitmproxy is on port 8888
echo Press Ctrl+C to stop
echo.

process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"

pause
