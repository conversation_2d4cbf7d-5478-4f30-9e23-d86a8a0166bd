@echo off
chcp 65001 >nul

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    goto :run_program
) else (
    echo 需要管理员权限，正在提升权限...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
    exit /b
)

:run_program
title Firefox Proxy Tool
echo ========================================
echo Firefox 流量代理工具
echo ========================================
echo.

:: 检查文件
if not exist "process-proxy.exe" (
    echo [✗] 错误: 未找到 process-proxy.exe
    echo 请先运行 compile.bat 编译程序
    pause
    exit /b 1
)

echo [✓] 程序文件检查完成
echo.

echo 配置信息:
echo - 目标进程: Firefox (firefox.exe)
echo - 代理端口: 8888
echo - 详细输出: 启用
echo - 统计信息: 启用
echo.

echo 使用说明:
echo 1. 确保 mitmproxy 在端口 8888 运行
echo    命令: mitmproxy -p 8888
echo 2. 启动 Firefox 浏览器
echo 3. 在 Firefox 中访问任意网站
echo 4. 观察下方的日志输出
echo 5. 按 Ctrl+C 停止程序
echo.

echo 正在启动代理工具...
echo ========================================
echo.

:: 启动代理工具，注意过滤器语法中的空格
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"

echo.
echo 程序已停止
pause
