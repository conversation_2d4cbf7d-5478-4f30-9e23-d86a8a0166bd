# 🎯 新解决方案：全局HTTP代理

## 🔍 **问题分析**

经过深入研究WinDivert文档，我发现了根本问题：

**WinDivert在网络层(WINDIVERT_LAYER_NETWORK)不支持进程过滤**

原来的过滤器 `"tcp && ProcessName == firefox.exe"` 在WinDivert网络层是无效的，这就是为什么一直出现错误代码87的原因。

## ✅ **新解决方案**

我已经重新设计了程序，采用**全局HTTP代理**的方式：

### 🔧 **工作原理**
1. **拦截所有出站HTTP流量**：使用过滤器 `"outbound and tcp.DstPort == 80"`
2. **重定向到代理端口**：将所有HTTP流量重定向到mitmproxy
3. **让mitmproxy处理过滤**：在mitmproxy层面进行进程或URL过滤

### 📋 **新的使用方法**

```bash
# 基本用法（拦截所有HTTP流量）
process-proxy.exe -v -s -p 8888

# 同时拦截HTTP和HTTPS
process-proxy.exe -v -s -p 8888 -f "outbound and (tcp.DstPort == 80 or tcp.DstPort == 443)"

# 只拦截HTTPS
process-proxy.exe -v -s -p 8888 -f "outbound and tcp.DstPort == 443"
```

## 🎯 **优势**

1. **稳定可靠**：使用WinDivert原生支持的过滤器
2. **简单有效**：避免了复杂的进程过滤逻辑
3. **全面覆盖**：可以拦截任何进程的HTTP流量
4. **灵活配置**：可以在mitmproxy层面进行精细过滤

## 🧪 **测试步骤**

### 方法1：使用测试脚本
```bash
# 以管理员身份运行
test-new-version.bat
```

### 方法2：手动测试
```bash
# 1. 启动mitmproxy
mitmproxy -p 8888

# 2. 以管理员身份运行代理工具
process-proxy.exe -v -s -p 8888

# 3. 在任意浏览器中访问HTTP网站
# 例如：http://httpbin.org/ip
```

## 📊 **预期结果**

成功运行时您会看到：

```
[2025-07-10 12:38:26][main:109] Process Proxy Tool v1.0 - Starting initialization...
[2025-07-10 12:38:26][main:175] Running with administrator privileges.
[2025-07-10 12:38:26][main:193] Opening WinDivert device with filter: outbound and tcp.DstPort == 80
[2025-07-10 12:38:26][main:206] WinDivert device opened successfully.
[2025-07-10 12:38:26][main:215] Entering main packet processing loop...

# 当访问HTTP网站时：
[2025-07-10 12:38:30][VERBOSE][main:235] Received packet #1 (length: 60 bytes)
[2025-07-10 12:38:30][VERBOSE][main:257] Redirecting HTTP traffic from port 80 to proxy port 8888
[2025-07-10 12:38:30][VERBOSE][print_packet_info:58] Packet #1 [REDIRECTED]: *************:54321 -> *************:8888
```

## 🔄 **与旧版本的区别**

| 特性 | 旧版本 | 新版本 |
|------|--------|--------|
| 过滤方式 | 进程名过滤（不工作） | 端口过滤（工作） |
| 拦截范围 | 特定进程 | 所有进程 |
| 稳定性 | 经常失败 | 稳定可靠 |
| 配置复杂度 | 复杂 | 简单 |

## 🎛️ **mitmproxy配置**

如果您想在mitmproxy层面进行进程过滤，可以使用mitmproxy的脚本功能：

```python
# mitmproxy_filter.py
def request(flow):
    # 这里可以添加自定义过滤逻辑
    # 例如：只处理特定域名的请求
    if "example.com" in flow.request.pretty_host:
        # 处理请求
        pass
    else:
        # 忽略请求
        pass
```

然后运行：
```bash
mitmproxy -p 8888 -s mitmproxy_filter.py
```

## 🚀 **立即测试**

现在您可以：

1. **以管理员身份运行**：
   ```bash
   test-new-version.bat
   ```

2. **或手动运行**：
   ```bash
   process-proxy.exe -v -s -p 8888
   ```

3. **在任意浏览器中访问**：
   - http://httpbin.org/ip
   - http://example.com
   - 任何HTTP网站

这个新版本应该能够成功拦截和重定向HTTP流量到mitmproxy！

## 📝 **重要提醒**

- ✅ **必须以管理员身份运行**
- ✅ **确保mitmproxy在端口8888运行**
- ✅ **访问HTTP网站进行测试（不是HTTPS）**
- ✅ **这个版本会拦截所有HTTP流量，不仅仅是Firefox**

这样就解决了之前的所有问题，应该能正常工作了！
