package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/google/gopacket"
	"github.com/google/gopacket/layers"
	"github.com/google/gopacket/pcap"
)

func main() {
	// 命令行参数解析
	processName := flag.String("process", "", "目标进程名称(如chrome.exe)")
	proxyAddr := flag.String("proxy", "127.0.0.1:8080", "mitmproxy代理地址")
	flag.Parse()

	if *processName == "" {
		log.Fatal("请指定目标进程名称")
	}

	// 初始化WinDivert驱动
	divert, err := initWinDivert(*processName)
	if err != nil {
		log.Fatalf("初始化WinDivert失败: %v", err)
	}
	defer divert.Close()

	// 启动代理服务器
	proxyServer := NewProxyServer(*proxyAddr)
	go proxyServer.Start()
	defer proxyServer.Stop()

	// 设置信号处理，优雅退出
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)

	fmt.Printf("正在监听进程: %s，代理地址: %s\n", *processName, *proxyAddr)
	fmt.Println("按 Ctrl+C 退出...")
	<-sigCh
}

// WinDivert包装类
type WinDivert struct {
	handle    *pcap.Handle
	filter    string
	processID map[int]bool // 目标进程ID集合
}

// 初始化WinDivert驱动
func initWinDivert(processName string) (*WinDivert, error) {
	// 查找目标进程ID
	pids, err := findProcessIDs(processName)
	if err != nil {
		return nil, fmt.Errorf("查找进程失败: %v", err)
	}

	if len(pids) == 0 {
		return nil, fmt.Errorf("未找到进程: %s", processName)
	}

	// 打开WinDivert设备
	handle, err := pcap.OpenLive("\\Device\\NPF_{YourAdapterGUID}", 65536, true, pcap.BlockForever)
	if err != nil {
		return nil, fmt.Errorf("打开网络设备失败: %v", err)
	}

	// 创建进程ID映射
	processIDMap := make(map[int]bool)
	for _, pid := range pids {
		processIDMap[pid] = true
	}

	// 设置过滤器（仅拦截TCP流量）
	filter := "tcp"
	if err := handle.SetBPFFilter(filter); err != nil {
		return nil, fmt.Errorf("设置过滤器失败: %v", err)
	}

	return &WinDivert{
		handle:    handle,
		filter:    filter,
		processID: processIDMap,
	}, nil
}

// 查找进程ID
func findProcessIDs(processName string) ([]int, error) {
	// Windows下实现进程查找的代码
	// 这里需要使用Windows API，如CreateToolhelp32Snapshot等
	// 简化示例，实际实现会更复杂
	return []int{1234, 5678}, nil // 示例PID
}

// 关闭WinDivert
func (wd *WinDivert) Close() {
	if wd.handle != nil {
		wd.handle.Close()
	}
}

// 代理服务器
type ProxyServer struct {
	proxyAddr string
	ctx       context.Context
	cancel    context.CancelFunc
}

// 创建新的代理服务器
func NewProxyServer(proxyAddr string) *ProxyServer {
	ctx, cancel := context.WithCancel(context.Background())
	return &ProxyServer{
		proxyAddr: proxyAddr,
		ctx:       ctx,
		cancel:    cancel,
	}
}

// 启动代理服务器
func (ps *ProxyServer) Start() {
	// 实际实现中，这里会启动一个TCP代理服务器
	// 接收来自目标进程的流量，并转发到mitmproxy
	fmt.Printf("代理服务器已启动: %s\n", ps.proxyAddr)

	// 示例代码，监听信号以退出
	<-ps.ctx.Done()
	fmt.Println("代理服务器已停止")
}

// 停止代理服务器
func (ps *ProxyServer) Stop() {
	ps.cancel()
}

// 处理网络数据包
func (wd *WinDivert) handlePacket(packet gopacket.Packet) {
	// 解析IP和TCP层
	ipLayer := packet.Layer(layers.LayerTypeIPv4)
	if ipLayer == nil {
		return
	}
	ip, _ := ipLayer.(*layers.IPv4)

	tcpLayer := packet.Layer(layers.LayerTypeTCP)
	if tcpLayer == nil {
		return
	}
	tcp, _ := tcpLayer.(*layers.TCP)

	// 检查是否为目标进程的流量
	// 实际实现中，需要通过Windows API关联TCP连接和进程ID
	if wd.isTargetProcess(tcp.SrcPort, tcp.DstPort) {
		// 重定向流量到mitmproxy
		wd.redirectToProxy(ip, tcp)
	}
}

// 检查是否为目标进程的流量
func (wd *WinDivert) isTargetProcess(srcPort, dstPort layers.TCPPort) bool {
	// 实际实现中，需要通过Windows API获取连接对应的进程ID
	// 简化示例，假设所有流量都是目标进程的
	return true
}

// 重定向流量到代理服务器
func (wd *WinDivert) redirectToProxy(ip *layers.IPv4, tcp *layers.TCP) {
	// 修改目标IP和端口为mitmproxy地址
	// 重新计算校验和
	// 发送修改后的数据包
	fmt.Printf("重定向流量: %s:%d -> %s:%d\n", ip.SrcIP, tcp.SrcPort, ip.DstIP, tcp.DstPort)
}    