# Process Proxy Tool 快速开始指南

## 🚀 快速启动（3步搞定）

### 步骤1：编译程序
```bash
compile.bat
```

### 步骤2：以管理员身份打开命令提示符
1. 按 `Win + R`，输入 `cmd`
2. 按 `Ctrl + Shift + Enter`（以管理员身份运行）
3. 导航到程序目录

### 步骤3：运行代理工具
```bash
# Firefox代理（注意空格）
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
```

## ⚡ 一键启动脚本

### 方法1：使用批处理文件
```bash
# 双击运行（会自动请求管理员权限）
test-firefox.bat
```

### 方法2：使用PowerShell脚本
```bash
# 右键PowerShell -> 以管理员身份运行
powershell -ExecutionPolicy Bypass -File Start-FirefoxProxy.ps1
```

## 🎯 完整测试流程

### 1. 准备环境
```bash
# 终端1：启动mitmproxy
mitmproxy -p 8888

# 终端2：以管理员身份运行代理工具
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
```

### 2. 测试拦截
1. 启动Firefox浏览器
2. 访问：http://httpbin.org/ip
3. 观察输出：
   - 代理工具显示 `[REDIRECTED]` 消息
   - mitmproxy显示拦截的HTTP请求

## 🔧 常见问题快速解决

### 问题1：错误代码87
```bash
# ❌ 错误写法
process-proxy.exe -f "tcp && ProcessName==firefox.exe"

# ✅ 正确写法（注意==前后的空格）
process-proxy.exe -f "tcp && ProcessName == firefox.exe"
```

### 问题2：权限不足
```bash
# 解决方案：以管理员身份运行命令提示符
# Win + R -> cmd -> Ctrl + Shift + Enter
```

### 问题3：没有拦截到数据包
```bash
# 检查进程是否运行
tasklist | findstr firefox.exe

# 使用详细模式查看
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
```

## 📋 支持的浏览器

| 浏览器 | 进程名 | 命令示例 |
|--------|--------|----------|
| Firefox | firefox.exe | `process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"` |
| Chrome | chrome.exe | `process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == chrome.exe"` |
| Edge | msedge.exe | `process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == msedge.exe"` |
| IE | iexplore.exe | `process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == iexplore.exe"` |

## 🎛️ 参数说明

| 参数 | 说明 | 示例 |
|------|------|------|
| `-v` | 详细输出模式 | 显示每个数据包的详细信息 |
| `-s` | 统计信息 | 每10秒显示统计数据 |
| `-p` | 代理端口 | `-p 8888` 设置代理端口为8888 |
| `-f` | 过滤器 | `-f "tcp && ProcessName == firefox.exe"` |
| `-h` | 帮助信息 | 显示所有可用参数 |

## 📊 成功运行的日志示例

```
[2025-07-10 12:15:17][main:101] Process Proxy Tool v1.0 - Starting initialization...
[2025-07-10 12:15:17][main:171] Running with administrator privileges.
[2025-07-10 12:15:17][main:177] WinDivert device opened successfully.
[2025-07-10 12:15:17][main:195] Entering main packet processing loop...
[2025-07-10 12:15:20][VERBOSE][main:225] Received packet #1 (length: 60 bytes)
[2025-07-10 12:15:20][VERBOSE][main:247] Redirecting HTTP traffic from port 80 to proxy port 8888
[2025-07-10 12:15:20][VERBOSE][print_packet_info:58] Packet #1 [REDIRECTED]: 192.168.1.100:54321 -> 93.184.216.34:8888 (len=60, proto=6)
[2025-07-10 12:15:30][main:210] Statistics: Total=15, Redirected=5, Rate=1.5 pkt/s
```

## 🆘 获取帮助

如果遇到问题：

1. **查看详细文档**：
   - `故障排除.md` - 详细的错误解决方案
   - `使用说明.md` - 完整的使用手册
   - `commands.txt` - 命令参考

2. **生成调试日志**：
   ```bash
   process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe" > debug.log 2>&1
   ```

3. **检查系统状态**：
   ```bash
   # 检查进程
   tasklist | findstr firefox
   
   # 检查端口
   netstat -an | findstr :8888
   
   # 检查文件
   dir process-proxy.exe WinDivert*
   ```

## ✅ 验证安装

运行以下命令验证一切正常：

```bash
# 1. 检查程序
process-proxy.exe -h

# 2. 检查权限（应该显示管理员权限警告）
process-proxy.exe -v -f "tcp && ProcessName == notepad.exe"
```

如果看到权限警告，说明程序工作正常，只需要以管理员身份运行即可。

---

**记住关键点**：
- ✅ 必须以管理员身份运行
- ✅ 过滤器中 `==` 前后必须有空格
- ✅ 确保目标进程正在运行
- ✅ 确保mitmproxy在指定端口运行
