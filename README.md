# Process Proxy Tool

一个基于WinDivert的进程流量代理工具，可以将指定进程的HTTP/HTTPS流量重定向到mitmproxy等代理服务器。

## 功能特性

- ✅ 支持按进程名过滤流量
- ✅ 自动重定向HTTP(80)和HTTPS(443)端口到代理
- ✅ 详细的日志输出便于调试
- ✅ 实时统计信息显示
- ✅ 灵活的命令行参数配置
- ✅ 自动校验和重计算

## 系统要求

- Windows 7/8/10/11 (64位)
- 管理员权限
- gcc编译器 (MinGW-w64推荐)
- WinDivert库

## 安装依赖

### 1. 安装gcc编译器

推荐使用MinGW-w64：
```bash
# 下载并安装MinGW-w64
# 或使用包管理器如Chocolatey
choco install mingw
```

### 2. 安装WinDivert

1. 从 [WinDivert官网](https://www.reqrypt.org/windivert.html) 下载最新版本
2. 解压到项目目录的 `WinDivert` 文件夹中
3. 确保以下文件存在：
   - `WinDivert/windivert.h`
   - `WinDivert/WinDivert.lib`
   - `WinDivert/WinDivert.dll`
   - `WinDivert/WinDivert64.sys`

## 编译

### 方法1: 使用批处理脚本（推荐）
```bash
# 双击运行或在命令行执行
build.bat
```

### 方法2: 使用Makefile
```bash
# 检查依赖
make check-deps

# 编译程序
make

# 安装（复制必要文件）
make install
```

### 方法3: 手动编译
```bash
gcc -Wall -Wextra -O2 -std=c99 -o process-proxy.exe main.c WinDivert.lib -lws2_32
```

## 使用方法

### 基本用法

```bash
# 显示帮助
process-proxy.exe -h

# 使用默认设置（Firefox，端口8080）
process-proxy.exe

# 启用详细输出
process-proxy.exe -v

# 启用统计信息
process-proxy.exe -s

# 指定代理端口
process-proxy.exe -p 8080

# 自定义进程过滤器
process-proxy.exe -f "tcp && ProcessName == chrome.exe"
```

### 完整示例

```bash
# 代理Chrome浏览器流量到端口8080，启用详细输出和统计
process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == chrome.exe"
```

## 命令行参数

| 参数 | 长参数 | 描述 | 默认值 |
|------|--------|------|--------|
| `-h` | `--help` | 显示帮助信息 | - |
| `-p` | `--port` | 指定代理端口 | 8080 |
| `-f` | `--filter` | 设置WinDivert过滤规则 | `tcp && ProcessName == firefox.exe` |
| `-v` | `--verbose` | 启用详细输出 | 关闭 |
| `-s` | `--stats` | 显示统计信息 | 关闭 |

## 测试

### 自动测试
```bash
# 以管理员身份运行测试脚本
test.bat
```

### 手动测试步骤

1. **启动mitmproxy**
   ```bash
   mitmproxy -p 8080 --set confdir=~/.mitmproxy
   ```

2. **启动代理工具**
   ```bash
   # 以管理员身份运行
   process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == firefox.exe"
   ```

3. **测试浏览器**
   - 启动Firefox浏览器
   - 访问任意HTTP/HTTPS网站
   - 观察代理工具和mitmproxy的日志输出

## 日志输出说明

### 普通日志
```
[2024-01-01 12:00:00][main:57] Starting proxy with filter: tcp && ProcessName == firefox.exe on port 8080
[2024-01-01 12:00:01][main:73] WinDivert device opened successfully.
```

### 详细日志（-v参数）
```
[2024-01-01 12:00:02][VERBOSE][print_packet_info:45] Packet #1 [RECEIVED]: 192.168.1.100:54321 -> 93.184.216.34:80 (len=60, proto=6)
[2024-01-01 12:00:02][VERBOSE][main:234] Redirecting HTTP traffic from port 80 to proxy port 8080
[2024-01-01 12:00:02][VERBOSE][print_packet_info:45] Packet #1 [REDIRECTED]: 192.168.1.100:54321 -> 93.184.216.34:8080 (len=60, proto=6)
```

### 统计信息（-s参数）
```
[2024-01-01 12:00:10][main:210] Statistics: Total=150, Redirected=45, Rate=15.0 pkt/s
```

## 故障排除

### 常见错误

1. **"failed to open WinDivert device"**
   - 确保以管理员身份运行
   - 检查WinDivert驱动是否正确安装
   - 验证过滤器语法是否正确

2. **"Not running as administrator"**
   - 右键点击程序，选择"以管理员身份运行"

3. **编译错误**
   - 检查gcc是否正确安装
   - 确保WinDivert库文件路径正确
   - 验证头文件是否存在

### 调试技巧

1. **使用详细模式**
   ```bash
   process-proxy.exe -v
   ```

2. **检查进程名**
   - 使用任务管理器确认目标进程的确切名称
   - 注意大小写敏感

3. **测试过滤器**
   ```bash
   # 简单测试所有TCP流量（谨慎使用）
   process-proxy.exe -v -f "tcp"
   ```

## 开发说明

### 代码结构
- `main.c` - 主程序文件
- `build.bat` - Windows编译脚本
- `test.bat` - 测试脚本
- `Makefile` - Make构建文件

### 编译选项
- `-Wall -Wextra` - 启用所有警告
- `-O2` - 优化级别2
- `-std=c99` - 使用C99标准

## 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。
