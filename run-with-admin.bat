@echo off
setlocal

:: Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running with administrator privileges
    goto :run_program
) else (
    echo [INFO] Need administrator privileges. Requesting elevation...
    
    :: Create a temporary VBS script to elevate privileges
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\elevate.vbs"
    echo UAC.ShellExecute "%~s0", "", "", "runas", 1 >> "%temp%\elevate.vbs"
    
    :: Run the VBS script
    cscript //nologo "%temp%\elevate.vbs"
    
    :: Clean up
    del "%temp%\elevate.vbs"
    exit /b
)

:run_program
title Process Proxy Tool - Administrator Mode
echo ========================================
echo Process Proxy Tool - Administrator Mode
echo ========================================
echo.

:: Change to script directory
cd /d "%~dp0"

:: Check required files
if not exist "process-proxy.exe" (
    echo [ERROR] process-proxy.exe not found
    echo Please compile the program first
    pause
    exit /b 1
)

echo [OK] Found process-proxy.exe
echo [OK] Running with administrator privileges
echo.

echo Configuration:
echo - Target Process: firefox.exe
echo - Proxy Port: 8888
echo - Verbose Output: Enabled
echo - Statistics: Enabled
echo.

echo Instructions:
echo 1. Make sure mitmproxy is running: mitmproxy -p 8888
echo 2. Start Firefox browser
echo 3. Visit any HTTP/HTTPS website
echo 4. Watch the output below for intercepted packets
echo 5. Press Ctrl+C to stop
echo.

echo Starting proxy tool...
echo ========================================
echo.

:: Run the proxy tool
process-proxy.exe -v -s -p 8888 -t firefox.exe

echo.
echo Proxy tool stopped
pause
