@echo off
chcp 65001 >nul

echo ========================================
echo 微信路由代理设置工具
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到管理员权限
) else (
    echo [✗] 需要管理员权限才能修改路由表
    echo 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 请选择操作:
echo 1. 设置微信流量路由到代理
echo 2. 恢复原始路由设置
echo 3. 查看当前路由表
echo 4. 退出
echo.
set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 正在设置微信流量路由...
    
    :: 获取微信常用服务器IP段
    echo [信息] 微信主要服务器IP段:
    echo - 腾讯云: **********/16, ***********/16
    echo - 微信服务器: ***********/16, ***********/16
    echo - CDN: ***********/16, ***********/16
    echo.
    
    :: 创建路由规则将微信流量重定向
    echo 添加路由规则...
    
    :: 注意：这里需要一个本地代理程序来接收重定向的流量
    :: 实际实现需要更复杂的网络配置
    
    echo [警告] 此方案需要复杂的网络配置，建议使用其他方案
    
) else if "%choice%"=="2" (
    echo.
    echo 恢复原始路由设置...
    
    :: 删除添加的路由规则
    echo 删除自定义路由规则...
    
    echo [✓] 路由设置已恢复
    
) else if "%choice%"=="3" (
    echo.
    echo 当前路由表:
    echo ----------------------------------------
    route print | findstr "0.0.0.0"
    
) else if "%choice%"=="4" (
    echo 退出程序
    exit /b 0
    
) else (
    echo 无效选择
    exit /b 1
)

echo.
pause
