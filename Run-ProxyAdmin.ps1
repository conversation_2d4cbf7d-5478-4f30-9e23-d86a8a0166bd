# Process Proxy Tool - Auto Admin Elevation
# Usage: Right-click -> Run with PowerShell

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Need administrator privileges. Requesting elevation..." -ForegroundColor Yellow
    
    # Get the current script path
    $scriptPath = $MyInvocation.MyCommand.Path
    $workingDir = Split-Path $scriptPath -Parent
    
    # Restart as administrator
    Start-Process PowerShell -Verb RunAs -ArgumentList "-NoProfile -ExecutionPolicy Bypass -Command `"Set-Location '$workingDir'; & '$scriptPath'`""
    exit
}

# If we reach here, we're running as administrator
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Process Proxy Tool - Administrator Mode" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "[OK] Running with administrator privileges" -ForegroundColor Green
Write-Host ""

# Check if process-proxy.exe exists
if (-not (Test-Path "process-proxy.exe")) {
    Write-Host "[ERROR] process-proxy.exe not found" -ForegroundColor Red
    Write-Host "Please compile the program first using compile.bat" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[OK] Found process-proxy.exe" -ForegroundColor Green
Write-Host ""

# Display configuration
Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Target Process: firefox.exe"
Write-Host "  Proxy Port: 8888"
Write-Host "  Verbose Output: Enabled"
Write-Host "  Statistics: Enabled"
Write-Host ""

# Display instructions
Write-Host "Instructions:" -ForegroundColor Yellow
Write-Host "  1. Make sure mitmproxy is running: mitmproxy -p 8888"
Write-Host "  2. Start Firefox browser"
Write-Host "  3. Visit any HTTP/HTTPS website (e.g., http://httpbin.org/ip)"
Write-Host "  4. Watch the output below for intercepted packets"
Write-Host "  5. Press Ctrl+C to stop the proxy"
Write-Host ""

Write-Host "Starting proxy tool..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Run the proxy tool
try {
    & ".\process-proxy.exe" -v -s -p 8888 -t firefox.exe
} catch {
    Write-Host ""
    Write-Host "[ERROR] Failed to start proxy tool: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Proxy tool stopped" -ForegroundColor Yellow
Read-Host "Press Enter to exit"
