# Proxifier强制代理方案

## 🎯 **方案概述**

Proxifier是一个专业的代理工具，可以强制任何应用程序通过指定的代理服务器连接，即使应用程序本身不支持代理。

## 📥 **安装步骤**

1. **下载Proxifier**
   - 官网: https://www.proxifier.com/
   - 下载Proxifier Standard Edition
   - 安装并获取试用许可证

2. **配置代理服务器**
   - 打开Proxifier
   - 菜单: Profile → Proxy Servers
   - 点击"Add"添加新代理
   - 配置如下:
     ```
     Address: 127.0.0.1
     Port: 8888
     Protocol: HTTP
     ```

3. **配置代理规则**
   - 菜单: Profile → Proxification Rules
   - 点击"Add"添加新规则
   - 配置如下:
     ```
     Name: WeChat Proxy
     Applications: WeChat.exe
     Target hosts: *
     Target ports: *
     Action: Proxy 127.0.0.1:8888
     ```

## 🎮 **使用步骤**

1. **启动mitmproxy**
   ```bash
   mitmproxy -p 8888 --set confdir=~/.mitmproxy
   ```

2. **启动Proxifier**
   - 确保代理规则已启用
   - 查看日志窗口

3. **启动微信**
   - 正常启动微信
   - 在Proxifier中应该能看到连接日志

4. **验证拦截**
   - 在微信中进行操作
   - 在mitmproxy中查看拦截的请求
   - 在Proxifier中查看连接统计

## ✅ **优势**

- ✅ 强制所有网络连接通过代理
- ✅ 支持所有协议（HTTP/HTTPS/TCP/UDP）
- ✅ 图形化界面，配置简单
- ✅ 实时连接监控
- ✅ 不需要修改系统设置

## ⚠️ **注意事项**

- 需要购买许可证（试用期30天）
- 可能被某些安全软件误报
- 需要管理员权限运行
