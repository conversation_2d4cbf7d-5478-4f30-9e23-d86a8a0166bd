@echo off
chcp 65001 >nul
echo ========================================
echo Process Proxy Tool 测试脚本
echo ========================================
echo.

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到管理员权限
) else (
    echo [✗] 错误: 需要管理员权限才能运行WinDivert
    echo 请右键点击此脚本，选择"以管理员身份运行"
    pause
    exit /b 1
)
echo.

:: 检查可执行文件是否存在
if not exist "process-proxy.exe" (
    echo [✗] 错误: 未找到 process-proxy.exe
    echo 请先运行 build.bat 编译程序
    pause
    exit /b 1
)
echo [✓] 找到可执行文件
echo.

:: 显示帮助信息
echo [测试1] 显示帮助信息:
echo ----------------------------------------
process-proxy.exe -h
echo.

:: 测试参数解析
echo [测试2] 测试参数解析（不实际运行）:
echo ----------------------------------------
echo 测试命令: process-proxy.exe -v -p 8080 -f "tcp"
echo 注意: 这个测试会立即失败，因为过滤器"tcp"会匹配所有TCP流量
echo 按任意键继续测试...
pause >nul
echo.

:: 提供交互式测试选项
echo [交互式测试选项]
echo ----------------------------------------
echo 1. 测试Firefox代理 (默认)
echo 2. 测试Chrome代理
echo 3. 测试自定义进程
echo 4. 退出
echo.
set /p choice="请选择测试选项 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 启动Firefox代理测试...
    echo 过滤器: tcp && ProcessName == firefox.exe
    echo 代理端口: 8080
    echo.
    echo 请确保:
    echo 1. Firefox浏览器已安装
    echo 2. mitmproxy在端口8080运行
    echo 3. 准备在Firefox中访问网站进行测试
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == firefox.exe"
) else if "%choice%"=="2" (
    echo.
    echo 启动Chrome代理测试...
    echo 过滤器: tcp && ProcessName == chrome.exe
    echo 代理端口: 8080
    echo.
    echo 请确保:
    echo 1. Chrome浏览器已安装
    echo 2. mitmproxy在端口8080运行
    echo 3. 准备在Chrome中访问网站进行测试
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == chrome.exe"
) else if "%choice%"=="3" (
    echo.
    set /p process_name="请输入进程名称 (例如: notepad.exe): "
    set /p proxy_port="请输入代理端口 (默认8080): "
    if "%proxy_port%"=="" set proxy_port=8080
    
    echo.
    echo 启动自定义进程代理测试...
    echo 过滤器: tcp && ProcessName == %process_name%
    echo 代理端口: %proxy_port%
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe -v -s -p %proxy_port% -f "tcp && ProcessName == %process_name%"
) else if "%choice%"=="4" (
    echo 退出测试
    exit /b 0
) else (
    echo 无效选择，退出
    exit /b 1
)

echo.
echo 测试完成！
pause
