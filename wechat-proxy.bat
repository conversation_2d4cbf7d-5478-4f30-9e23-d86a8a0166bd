@echo off
chcp 65001 >nul

echo ========================================
echo 微信流量拦截代理工具
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到管理员权限
) else (
    echo [✗] 需要管理员权限才能拦截网络流量
    echo 请右键选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 检查微信进程
tasklist | findstr "WeChat.exe" >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到微信进程正在运行
) else (
    echo [!] 警告: 未检测到微信进程
    echo 请先启动微信，然后重新运行此脚本
    set /p continue="是否继续？(y/n): "
    if /i not "%continue%"=="y" exit /b 1
)

:: 检查mitmproxy
echo.
echo [检查] 请确保mitmproxy已在端口8888运行
echo 如果未启动，请在另一个终端运行: mitmproxy -p 8888
echo.
set /p ready="mitmproxy是否已准备就绪？(y/n): "
if /i not "%ready%"=="y" (
    echo 请先启动mitmproxy，然后重新运行此脚本
    pause
    exit /b 1
)

echo.
echo 配置信息:
echo - 目标进程: WeChat.exe (微信)
echo - 代理端口: 8888
echo - 拦截类型: 所有TCP流量
echo - 详细输出: 启用
echo - 统计信息: 启用
echo.

echo 微信网络分析说明:
echo 1. 微信使用HTTPS加密通信，mitmproxy需要安装证书才能解密
echo 2. 微信可能使用证书绑定，部分流量可能无法解密
echo 3. 建议同时监控DNS查询以了解连接的域名
echo 4. 微信的长连接和短连接都会被拦截
echo.

echo 开始拦截微信流量...
echo 请在微信中进行操作（发消息、刷朋友圈等）来生成网络流量
echo 按 Ctrl+C 停止拦截
echo ========================================
echo.

:: 使用更广泛的过滤器来捕获微信的所有网络活动
:: 微信可能使用多个端口，不仅仅是80/443
process-proxy.exe -v -s -p 8888 -f "outbound and tcp and (tcp.DstPort == 80 or tcp.DstPort == 443 or tcp.DstPort == 8080 or tcp.DstPort == 14000)"

echo.
echo 微信流量拦截已停止
pause
