@echo off

:: Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    goto :run_program
) else (
    echo Need administrator privileges. Elevating...
    powershell -Command "Start-Process cmd -ArgumentList '/c \"%~f0\"' -Verb RunAs"
    exit /b
)

:run_program
title Firefox Proxy Tool
echo ========================================
echo Firefox Traffic Proxy Tool
echo ========================================
echo.

:: Check files
if not exist "process-proxy.exe" (
    echo ERROR: process-proxy.exe not found
    echo Please run compile.bat first
    pause
    exit /b 1
)

echo [OK] Program files found
echo.

echo Configuration:
echo - Target: Firefox (firefox.exe)
echo - Proxy port: 8888
echo - Verbose output: Enabled
echo - Statistics: Enabled
echo.

echo Instructions:
echo 1. Make sure mitmproxy is running on port 8888
echo    Command: mitmproxy -p 8888
echo 2. Start Firefox browser
echo 3. Visit any website in Firefox
echo 4. Watch the log output below
echo 5. Press Ctrl+C to stop
echo.

echo Starting proxy tool...
echo ========================================
echo.

:: Start proxy tool with correct filter syntax (note the spaces around ==)
process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"

echo.
echo Program stopped
pause
