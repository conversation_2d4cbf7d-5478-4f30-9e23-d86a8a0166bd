// 微信网络流量捕获 Frida 脚本
// 使用方法: frida -p <WeChat进程ID> -l wechat_network_hook.js

console.log("[+] 微信网络流量捕获脚本已启动");
console.log("[+] 正在hook网络相关API...");

// 全局变量
var logFile = null;
var connectionCount = 0;

// 工具函数：将字节数组转换为十六进制字符串
function bytesToHex(bytes, length) {
    var result = "";
    for (var i = 0; i < length && i < bytes.length; i++) {
        var hex = bytes[i].toString(16);
        result += (hex.length === 1 ? "0" + hex : hex) + " ";
    }
    return result;
}

// 工具函数：尝试将字节数组转换为字符串
function bytesToString(bytes, length) {
    try {
        var result = "";
        for (var i = 0; i < length && i < bytes.length; i++) {
            var char = bytes[i];
            if (char >= 32 && char <= 126) {
                result += String.fromCharCode(char);
            } else {
                result += ".";
            }
        }
        return result;
    } catch (e) {
        return "[无法解析]";
    }
}

// 工具函数：记录日志
function logTraffic(direction, data) {
    var timestamp = new Date().toISOString();
    var logEntry = `[${timestamp}] ${direction}: ${JSON.stringify(data)}\n`;
    console.log(logEntry.trim());
}

// Hook Winsock API
try {
    // Hook send函数
    var sendPtr = Module.findExportByName("ws2_32.dll", "send");
    if (sendPtr) {
        Interceptor.attach(sendPtr, {
            onEnter: function(args) {
                this.socket = args[0];
                this.buffer = args[1];
                this.length = args[2].toInt32();
                this.flags = args[3];
            },
            onLeave: function(retval) {
                if (retval.toInt32() > 0 && this.length > 0) {
                    try {
                        var data = Memory.readByteArray(this.buffer, Math.min(this.length, 1024));
                        var dataArray = new Uint8Array(data);
                        
                        logTraffic("SEND", {
                            socket: this.socket.toString(),
                            length: this.length,
                            sent: retval.toInt32(),
                            hex: bytesToHex(dataArray, 64),
                            ascii: bytesToString(dataArray, 64)
                        });
                    } catch (e) {
                        console.log("[-] Send hook error:", e);
                    }
                }
            }
        });
        console.log("[+] send() API已hook");
    }

    // Hook recv函数
    var recvPtr = Module.findExportByName("ws2_32.dll", "recv");
    if (recvPtr) {
        Interceptor.attach(recvPtr, {
            onEnter: function(args) {
                this.socket = args[0];
                this.buffer = args[1];
                this.length = args[2].toInt32();
                this.flags = args[3];
            },
            onLeave: function(retval) {
                if (retval.toInt32() > 0) {
                    try {
                        var data = Memory.readByteArray(this.buffer, retval.toInt32());
                        var dataArray = new Uint8Array(data);
                        
                        logTraffic("RECV", {
                            socket: this.socket.toString(),
                            length: retval.toInt32(),
                            hex: bytesToHex(dataArray, 64),
                            ascii: bytesToString(dataArray, 64)
                        });
                    } catch (e) {
                        console.log("[-] Recv hook error:", e);
                    }
                }
            }
        });
        console.log("[+] recv() API已hook");
    }

    // Hook connect函数
    var connectPtr = Module.findExportByName("ws2_32.dll", "connect");
    if (connectPtr) {
        Interceptor.attach(connectPtr, {
            onEnter: function(args) {
                this.socket = args[0];
                this.addr = args[1];
                this.addrlen = args[2];
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    try {
                        // 解析sockaddr结构
                        var family = Memory.readU16(this.addr);
                        if (family === 2) { // AF_INET
                            var port = Memory.readU16(this.addr.add(2));
                            var ip = Memory.readU32(this.addr.add(4));
                            var ipStr = ((ip & 0xFF)) + "." + 
                                       ((ip >> 8) & 0xFF) + "." + 
                                       ((ip >> 16) & 0xFF) + "." + 
                                       ((ip >> 24) & 0xFF);
                            
                            connectionCount++;
                            logTraffic("CONNECT", {
                                connection_id: connectionCount,
                                socket: this.socket.toString(),
                                ip: ipStr,
                                port: ((port & 0xFF) << 8) | ((port >> 8) & 0xFF)
                            });
                        }
                    } catch (e) {
                        console.log("[-] Connect hook error:", e);
                    }
                }
            }
        });
        console.log("[+] connect() API已hook");
    }

} catch (e) {
    console.log("[-] Winsock hook失败:", e);
}

// Hook WinHTTP API
try {
    var winHttpSendRequestPtr = Module.findExportByName("winhttp.dll", "WinHttpSendRequest");
    if (winHttpSendRequestPtr) {
        Interceptor.attach(winHttpSendRequestPtr, {
            onEnter: function(args) {
                this.hRequest = args[0];
                this.lpszHeaders = args[1];
                this.dwHeadersLength = args[2];
                this.lpOptional = args[3];
                this.dwOptionalLength = args[4];
                
                try {
                    var headers = "";
                    if (!this.lpszHeaders.isNull() && this.dwHeadersLength.toInt32() > 0) {
                        headers = Memory.readUtf16String(this.lpszHeaders, this.dwHeadersLength.toInt32());
                    }
                    
                    var body = "";
                    if (!this.lpOptional.isNull() && this.dwOptionalLength.toInt32() > 0) {
                        var bodyData = Memory.readByteArray(this.lpOptional, Math.min(this.dwOptionalLength.toInt32(), 1024));
                        var bodyArray = new Uint8Array(bodyData);
                        body = bytesToString(bodyArray, bodyArray.length);
                    }
                    
                    logTraffic("HTTP_SEND", {
                        request: this.hRequest.toString(),
                        headers: headers,
                        body: body.substring(0, 200) + (body.length > 200 ? "..." : ""),
                        body_length: this.dwOptionalLength.toInt32()
                    });
                } catch (e) {
                    console.log("[-] WinHttpSendRequest hook error:", e);
                }
            }
        });
        console.log("[+] WinHttpSendRequest() API已hook");
    }

    var winHttpReceiveResponsePtr = Module.findExportByName("winhttp.dll", "WinHttpReceiveResponse");
    if (winHttpReceiveResponsePtr) {
        Interceptor.attach(winHttpReceiveResponsePtr, {
            onEnter: function(args) {
                this.hRequest = args[0];
            },
            onLeave: function(retval) {
                if (retval.toInt32() !== 0) {
                    logTraffic("HTTP_RESPONSE", {
                        request: this.hRequest.toString(),
                        success: true
                    });
                }
            }
        });
        console.log("[+] WinHttpReceiveResponse() API已hook");
    }
} catch (e) {
    console.log("[-] WinHTTP hook失败:", e);
}

// Hook SSL/TLS函数
try {
    // 尝试hook常见的SSL库
    var sslLibraries = ["ssleay32.dll", "libssl-1_1.dll", "libssl-3.dll"];
    
    sslLibraries.forEach(function(libName) {
        try {
            var sslWritePtr = Module.findExportByName(libName, "SSL_write");
            if (sslWritePtr) {
                Interceptor.attach(sslWritePtr, {
                    onEnter: function(args) {
                        this.ssl = args[0];
                        this.buf = args[1];
                        this.num = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() > 0) {
                            try {
                                var data = Memory.readByteArray(this.buf, Math.min(this.num, 1024));
                                var dataArray = new Uint8Array(data);
                                
                                logTraffic("SSL_WRITE", {
                                    ssl: this.ssl.toString(),
                                    length: this.num,
                                    written: retval.toInt32(),
                                    data: bytesToString(dataArray, Math.min(200, dataArray.length))
                                });
                            } catch (e) {
                                console.log("[-] SSL_write hook error:", e);
                            }
                        }
                    }
                });
                console.log(`[+] SSL_write() from ${libName} 已hook`);
            }

            var sslReadPtr = Module.findExportByName(libName, "SSL_read");
            if (sslReadPtr) {
                Interceptor.attach(sslReadPtr, {
                    onEnter: function(args) {
                        this.ssl = args[0];
                        this.buf = args[1];
                        this.num = args[2].toInt32();
                    },
                    onLeave: function(retval) {
                        if (retval.toInt32() > 0) {
                            try {
                                var data = Memory.readByteArray(this.buf, retval.toInt32());
                                var dataArray = new Uint8Array(data);
                                
                                logTraffic("SSL_READ", {
                                    ssl: this.ssl.toString(),
                                    length: retval.toInt32(),
                                    data: bytesToString(dataArray, Math.min(200, dataArray.length))
                                });
                            } catch (e) {
                                console.log("[-] SSL_read hook error:", e);
                            }
                        }
                    }
                });
                console.log(`[+] SSL_read() from ${libName} 已hook`);
            }
        } catch (e) {
            // 库不存在，继续尝试下一个
        }
    });
} catch (e) {
    console.log("[-] SSL hook失败:", e);
}

// 监听进程退出
Process.setExceptionHandler(function(details) {
    console.log("[-] 异常:", details);
    return true;
});

console.log("[+] 所有hook已设置完成");
console.log("[+] 开始监控微信网络流量...");
console.log("[+] 按Ctrl+C停止监控");

// 定期输出统计信息
setInterval(function() {
    console.log(`[*] 统计: 已建立连接 ${connectionCount} 个`);
}, 30000); // 每30秒输出一次
