@echo off
chcp 65001 >nul

echo ========================================
echo 系统代理设置工具
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到管理员权限
) else (
    echo [!] 警告: 建议以管理员身份运行以确保设置生效
)
echo.

echo 请选择操作:
echo 1. 启用系统代理 (localhost:8888)
echo 2. 禁用系统代理
echo 3. 查看当前代理设置
echo 4. 退出
echo.
set /p choice="请选择 (1-4): "

if "%choice%"=="1" (
    echo.
    echo 正在设置系统代理为 localhost:8888...
    
    :: 设置HTTP代理
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 1 /f >nul
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer /t REG_SZ /d "localhost:8888" /f >nul
    
    :: 设置代理覆盖（绕过本地地址）
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyOverride /t REG_SZ /d "localhost;127.*;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*;<local>" /f >nul
    
    echo [✓] 系统代理已设置为 localhost:8888
    echo [!] 请重启浏览器以使设置生效
    
) else if "%choice%"=="2" (
    echo.
    echo 正在禁用系统代理...
    
    :: 禁用代理
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable /t REG_DWORD /d 0 /f >nul
    
    echo [✓] 系统代理已禁用
    echo [!] 请重启浏览器以使设置生效
    
) else if "%choice%"=="3" (
    echo.
    echo 当前代理设置:
    echo ----------------------------------------
    
    :: 查询代理状态
    for /f "tokens=3" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyEnable 2^>nul ^| find "ProxyEnable"') do set proxy_enabled=%%a
    for /f "tokens=3*" %%a in ('reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Internet Settings" /v ProxyServer 2^>nul ^| find "ProxyServer"') do set proxy_server=%%a %%b
    
    if "%proxy_enabled%"=="0x1" (
        echo 代理状态: 启用
        echo 代理服务器: %proxy_server%
    ) else (
        echo 代理状态: 禁用
    )
    
) else if "%choice%"=="4" (
    echo 退出程序
    exit /b 0
    
) else (
    echo 无效选择
    exit /b 1
)

echo.
echo ========================================
echo 重要提醒:
echo 1. 确保 mitmproxy 在端口 8888 运行
echo 2. 重启浏览器以使代理设置生效
echo 3. 使用完毕后记得禁用代理
echo ========================================
pause
