// 微信高级网络流量捕获脚本
// 专门针对微信内部函数和协议的hook
// 使用方法: frida -p <WeChat进程ID> -l wechat_advanced_hook.js

console.log("[+] 微信高级网络流量捕获脚本已启动");

// 配置选项
var CONFIG = {
    logToFile: true,
    maxDataLength: 1024,
    showHexDump: true,
    filterEmptyData: true,
    logLevel: "INFO" // DEBUG, INFO, WARN, ERROR
};

// 全局统计
var STATS = {
    connections: 0,
    httpRequests: 0,
    sslConnections: 0,
    dataBytes: 0
};

// 日志函数
function log(level, message, data) {
    var timestamp = new Date().toISOString();
    var logEntry = {
        timestamp: timestamp,
        level: level,
        message: message,
        data: data || {}
    };
    
    console.log(`[${timestamp}][${level}] ${message}`);
    if (data && Object.keys(data).length > 0) {
        console.log(JSON.stringify(data, null, 2));
    }
}

// 数据处理函数
function processNetworkData(buffer, length, direction) {
    try {
        if (length <= 0 || (CONFIG.filterEmptyData && length === 0)) {
            return null;
        }
        
        var data = Memory.readByteArray(buffer, Math.min(length, CONFIG.maxDataLength));
        var dataArray = new Uint8Array(data);
        
        // 尝试检测数据类型
        var dataType = detectDataType(dataArray);
        
        var result = {
            direction: direction,
            length: length,
            type: dataType,
            preview: bytesToString(dataArray, Math.min(100, dataArray.length))
        };
        
        if (CONFIG.showHexDump && length <= 256) {
            result.hex = bytesToHex(dataArray, dataArray.length);
        }
        
        // 特殊处理HTTP数据
        if (dataType === "HTTP") {
            result.http = parseHttpData(dataArray);
        }
        
        // 特殊处理微信协议数据
        if (dataType === "WECHAT") {
            result.wechat = parseWeChatData(dataArray);
        }
        
        STATS.dataBytes += length;
        return result;
        
    } catch (e) {
        log("ERROR", "处理网络数据时出错", {error: e.toString()});
        return null;
    }
}

// 检测数据类型
function detectDataType(dataArray) {
    if (dataArray.length < 4) return "UNKNOWN";
    
    var header = String.fromCharCode.apply(null, dataArray.slice(0, 8));
    
    // HTTP检测
    if (header.startsWith("GET ") || header.startsWith("POST ") || 
        header.startsWith("PUT ") || header.startsWith("DELETE ") ||
        header.startsWith("HTTP/")) {
        return "HTTP";
    }
    
    // HTTPS/TLS检测
    if (dataArray[0] === 0x16 && dataArray[1] === 0x03) {
        return "TLS";
    }
    
    // 微信协议检测（基于常见特征）
    if (dataArray.length >= 16) {
        // 检查是否包含微信特征字节
        var hasWeChatSignature = false;
        for (var i = 0; i < Math.min(dataArray.length - 4, 32); i++) {
            if (dataArray[i] === 0xBE && dataArray[i+1] === 0xEF) {
                hasWeChatSignature = true;
                break;
            }
        }
        if (hasWeChatSignature) return "WECHAT";
    }
    
    // JSON检测
    if (header.includes("{") || header.includes("[")) {
        return "JSON";
    }
    
    // XML检测
    if (header.includes("<")) {
        return "XML";
    }
    
    return "BINARY";
}

// 解析HTTP数据
function parseHttpData(dataArray) {
    try {
        var httpText = String.fromCharCode.apply(null, dataArray);
        var lines = httpText.split('\r\n');
        
        var result = {
            method: "",
            url: "",
            headers: {},
            body: ""
        };
        
        if (lines.length > 0) {
            var firstLine = lines[0].split(' ');
            if (firstLine.length >= 3) {
                result.method = firstLine[0];
                result.url = firstLine[1];
            }
        }
        
        var headerEnd = false;
        var bodyStart = 0;
        
        for (var i = 1; i < lines.length; i++) {
            if (lines[i] === "" && !headerEnd) {
                headerEnd = true;
                bodyStart = i + 1;
                break;
            }
            
            if (!headerEnd && lines[i].includes(":")) {
                var headerParts = lines[i].split(":");
                if (headerParts.length >= 2) {
                    result.headers[headerParts[0].trim()] = headerParts.slice(1).join(":").trim();
                }
            }
        }
        
        if (bodyStart < lines.length) {
            result.body = lines.slice(bodyStart).join('\r\n');
        }
        
        return result;
    } catch (e) {
        return {error: e.toString()};
    }
}

// 解析微信协议数据
function parseWeChatData(dataArray) {
    try {
        var result = {
            length: dataArray.length,
            signature: "",
            command: "",
            data: ""
        };
        
        // 简单的微信协议解析（需要根据实际协议调整）
        if (dataArray.length >= 8) {
            result.signature = bytesToHex(dataArray.slice(0, 4), 4);
            result.command = bytesToHex(dataArray.slice(4, 8), 4);
        }
        
        if (dataArray.length > 16) {
            result.data = bytesToString(dataArray.slice(16), Math.min(100, dataArray.length - 16));
        }
        
        return result;
    } catch (e) {
        return {error: e.toString()};
    }
}

// 工具函数
function bytesToHex(bytes, length) {
    var result = "";
    for (var i = 0; i < length && i < bytes.length; i++) {
        var hex = bytes[i].toString(16);
        result += (hex.length === 1 ? "0" + hex : hex) + " ";
    }
    return result.trim();
}

function bytesToString(bytes, length) {
    var result = "";
    for (var i = 0; i < length && i < bytes.length; i++) {
        var char = bytes[i];
        if (char >= 32 && char <= 126) {
            result += String.fromCharCode(char);
        } else if (char === 10) {
            result += "\\n";
        } else if (char === 13) {
            result += "\\r";
        } else if (char === 9) {
            result += "\\t";
        } else {
            result += ".";
        }
    }
    return result;
}

// Hook网络API
function hookNetworkAPIs() {
    // Hook send
    var sendPtr = Module.findExportByName("ws2_32.dll", "send");
    if (sendPtr) {
        Interceptor.attach(sendPtr, {
            onEnter: function(args) {
                this.socket = args[0];
                this.buffer = args[1];
                this.length = args[2].toInt32();
            },
            onLeave: function(retval) {
                if (retval.toInt32() > 0) {
                    var data = processNetworkData(this.buffer, this.length, "SEND");
                    if (data) {
                        log("INFO", "网络发送", {
                            socket: this.socket.toString(),
                            sent_bytes: retval.toInt32(),
                            ...data
                        });
                    }
                }
            }
        });
        log("INFO", "已hook send() API");
    }
    
    // Hook recv
    var recvPtr = Module.findExportByName("ws2_32.dll", "recv");
    if (recvPtr) {
        Interceptor.attach(recvPtr, {
            onEnter: function(args) {
                this.socket = args[0];
                this.buffer = args[1];
                this.length = args[2].toInt32();
            },
            onLeave: function(retval) {
                if (retval.toInt32() > 0) {
                    var data = processNetworkData(this.buffer, retval.toInt32(), "RECV");
                    if (data) {
                        log("INFO", "网络接收", {
                            socket: this.socket.toString(),
                            received_bytes: retval.toInt32(),
                            ...data
                        });
                    }
                }
            }
        });
        log("INFO", "已hook recv() API");
    }
    
    // Hook connect
    var connectPtr = Module.findExportByName("ws2_32.dll", "connect");
    if (connectPtr) {
        Interceptor.attach(connectPtr, {
            onEnter: function(args) {
                this.socket = args[0];
                this.addr = args[1];
            },
            onLeave: function(retval) {
                if (retval.toInt32() === 0) {
                    STATS.connections++;
                    try {
                        var family = Memory.readU16(this.addr);
                        if (family === 2) { // AF_INET
                            var port = Memory.readU16(this.addr.add(2));
                            var ip = Memory.readU32(this.addr.add(4));
                            var ipStr = ((ip & 0xFF)) + "." + 
                                       ((ip >> 8) & 0xFF) + "." + 
                                       ((ip >> 16) & 0xFF) + "." + 
                                       ((ip >> 24) & 0xFF);
                            
                            log("INFO", "新建连接", {
                                socket: this.socket.toString(),
                                ip: ipStr,
                                port: ((port & 0xFF) << 8) | ((port >> 8) & 0xFF),
                                connection_count: STATS.connections
                            });
                        }
                    } catch (e) {
                        log("ERROR", "解析连接地址失败", {error: e.toString()});
                    }
                }
            }
        });
        log("INFO", "已hook connect() API");
    }
}

// 启动hook
try {
    hookNetworkAPIs();
    
    // 定期输出统计信息
    setInterval(function() {
        log("INFO", "流量统计", STATS);
    }, 60000); // 每分钟输出一次
    
    log("INFO", "微信网络流量监控已启动");
    log("INFO", "配置", CONFIG);
    
} catch (e) {
    log("ERROR", "启动失败", {error: e.toString()});
}

// 优雅退出处理
Process.setExceptionHandler(function(details) {
    log("ERROR", "程序异常", details);
    return true;
});
