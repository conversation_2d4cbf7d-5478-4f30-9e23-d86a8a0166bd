@echo off
chcp 65001 >nul

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 已检测到管理员权限
    goto :run_program
) else (
    echo [!] 需要管理员权限才能运行WinDivert
    echo [!] 正在尝试以管理员身份重新启动...
    
    :: 创建临时VBS脚本来提升权限
    echo Set UAC = CreateObject^("Shell.Application"^) > "%temp%\getadmin.vbs"
    echo UAC.ShellExecute "cmd.exe", "/c ""%~s0""", "", "runas", 1 >> "%temp%\getadmin.vbs"
    
    "%temp%\getadmin.vbs"
    del "%temp%\getadmin.vbs"
    exit /B
)

:run_program
echo ========================================
echo Process Proxy Tool - 管理员模式
echo ========================================
echo.

:: 显示当前目录和文件
echo 当前目录: %CD%
echo.

:: 检查必要文件
if not exist "process-proxy.exe" (
    echo [✗] 错误: 未找到 process-proxy.exe
    echo 请先运行 compile.bat 编译程序
    pause
    exit /b 1
)

if not exist "WinDivert.dll" (
    echo [✗] 错误: 未找到 WinDivert.dll
    echo 请确保运行时文件已正确复制
    pause
    exit /b 1
)

echo [✓] 找到必要文件
echo.

:: 提供预设选项
echo 请选择运行模式:
echo.
echo 1. Firefox代理 (端口8888, 详细输出)
echo 2. Chrome代理 (端口8888, 详细输出)
echo 3. Edge代理 (端口8888, 详细输出)
echo 4. 自定义设置
echo 5. 显示帮助
echo 6. 退出
echo.
set /p choice="请选择 (1-6): "

if "%choice%"=="1" (
    echo.
    echo 启动Firefox代理模式...
    echo 过滤器: tcp && ProcessName == firefox.exe
    echo 代理端口: 8888
    echo 详细输出: 启用
    echo 统计信息: 启用
    echo.
    echo 请确保:
    echo 1. Firefox浏览器已安装并运行
    echo 2. mitmproxy在端口8888运行: mitmproxy -p 8888
    echo 3. 准备在Firefox中访问网站进行测试
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
    
) else if "%choice%"=="2" (
    echo.
    echo 启动Chrome代理模式...
    echo 过滤器: tcp && ProcessName == chrome.exe
    echo 代理端口: 8888
    echo.
    echo 请确保:
    echo 1. Chrome浏览器已安装并运行
    echo 2. mitmproxy在端口8888运行: mitmproxy -p 8888
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == chrome.exe"
    
) else if "%choice%"=="3" (
    echo.
    echo 启动Edge代理模式...
    echo 过滤器: tcp && ProcessName == msedge.exe
    echo 代理端口: 8888
    echo.
    echo 请确保:
    echo 1. Edge浏览器已安装并运行
    echo 2. mitmproxy在端口8888运行: mitmproxy -p 8888
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe -v -s -p 8888 -f "tcp && ProcessName == msedge.exe"
    
) else if "%choice%"=="4" (
    echo.
    echo 自定义设置模式
    echo.
    set /p process_name="请输入进程名称 (例如: firefox.exe): "
    set /p proxy_port="请输入代理端口 (默认8888): "
    if "%proxy_port%"=="" set proxy_port=8888
    
    set /p enable_verbose="启用详细输出? (y/n, 默认y): "
    if "%enable_verbose%"=="" set enable_verbose=y
    
    set /p enable_stats="启用统计信息? (y/n, 默认y): "
    if "%enable_stats%"=="" set enable_stats=y
    
    set verbose_flag=
    if /i "%enable_verbose%"=="y" set verbose_flag=-v
    
    set stats_flag=
    if /i "%enable_stats%"=="y" set stats_flag=-s
    
    echo.
    echo 启动自定义代理模式...
    echo 过滤器: tcp && ProcessName == %process_name%
    echo 代理端口: %proxy_port%
    echo 详细输出: %enable_verbose%
    echo 统计信息: %enable_stats%
    echo.
    echo 按Ctrl+C停止程序
    echo ----------------------------------------
    process-proxy.exe %verbose_flag% %stats_flag% -p %proxy_port% -f "tcp && ProcessName == %process_name%"
    
) else if "%choice%"=="5" (
    echo.
    echo 显示帮助信息:
    echo ----------------------------------------
    process-proxy.exe -h
    
) else if "%choice%"=="6" (
    echo 退出程序
    exit /b 0
    
) else (
    echo 无效选择，退出
    exit /b 1
)

echo.
echo 程序已结束
pause
