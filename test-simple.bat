@echo off

echo Simple WinDivert Test
echo ====================

echo Checking admin privileges...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [OK] Running as administrator
) else (
    echo [ERROR] Need administrator privileges
    echo Please run as administrator
    pause
    exit /b 1
)

echo.
echo Testing basic TCP filter...
echo This will capture ALL TCP traffic (use Ctrl+C to stop quickly)
echo.

process-proxy.exe -v -p 8888 -f "tcp.DstPort == 80"

pause
