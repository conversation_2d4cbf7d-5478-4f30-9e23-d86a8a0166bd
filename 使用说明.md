# Process Proxy Tool 使用说明

## 概述

这是一个基于WinDivert的进程流量代理工具，可以将指定进程的HTTP/HTTPS流量重定向到mitmproxy等代理服务器，便于进行流量分析和调试。

## 主要改进

### 方案一优化内容

1. **详细日志输出**
   - 添加时间戳显示
   - 分级日志（普通/详细模式）
   - 数据包详细信息显示
   - 错误代码和原因分析

2. **增强的调试功能**
   - 管理员权限检查
   - WinDivert驱动状态检查
   - 实时统计信息
   - 数据包计数和速率显示

3. **改进的错误处理**
   - 详细的错误信息
   - 故障排除建议
   - 优雅的错误恢复

4. **更好的用户体验**
   - 彩色输出和格式化
   - 进度指示
   - 交互式帮助

## 编译方法

### 快速编译
```bash
# 双击运行或在命令行执行
compile.bat
```

### 手动编译
```bash
gcc -Wall -Wextra -O2 -std=c99 -I"WinDivert-2.2.2-A\include" -o process-proxy.exe main.c "WinDivert-2.2.2-A\x64\WinDivert.lib" -lws2_32
```

## 使用方法

### 基本命令

```bash
# 显示帮助
process-proxy.exe -h

# 使用默认设置（Firefox，端口8080）
process-proxy.exe

# 启用详细输出
process-proxy.exe -v

# 启用统计信息
process-proxy.exe -s

# 指定代理端口
process-proxy.exe -p 8080

# 自定义进程过滤器
process-proxy.exe -f "tcp && ProcessName == chrome.exe"
```

### 完整示例

```bash
# 代理Chrome浏览器流量到端口8080，启用详细输出和统计
process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == chrome.exe"
```

## 日志输出详解

### 1. 普通日志
```
[2025-07-10 12:09:50][main:101] Process Proxy Tool v1.0 - Starting initialization...
[2025-07-10 12:09:50][main:145] Configuration complete:
[2025-07-10 12:09:50][main:146]   - Proxy port: 8080
[2025-07-10 12:09:50][main:147]   - Filter rule: tcp && ProcessName == firefox.exe
[2025-07-10 12:09:50][main:148]   - Verbose mode: enabled
[2025-07-10 12:09:50][main:149]   - Statistics: enabled
```

### 2. 详细日志（-v参数）
```
[2025-07-10 12:09:52][VERBOSE][main:225] Received packet #1 (length: 60 bytes)
[2025-07-10 12:09:52][VERBOSE][print_packet_info:58] Packet #1 [RECEIVED]: *************:54321 -> 93.184.216.34:80 (len=60, proto=6)
[2025-07-10 12:09:52][VERBOSE][main:247] Redirecting HTTP traffic from port 80 to proxy port 8080
[2025-07-10 12:09:52][VERBOSE][print_packet_info:58] Packet #1 [REDIRECTED]: *************:54321 -> 93.184.216.34:8080 (len=60, proto=6)
```

### 3. 统计信息（-s参数）
```
[2025-07-10 12:10:00][main:210] Statistics: Total=150, Redirected=45, Rate=15.0 pkt/s
```

### 4. 错误信息
```
[2025-07-10 12:09:50][main:175] Error: failed to open WinDivert device (error code: 5)
[2025-07-10 12:09:50][main:176] Possible causes:
[2025-07-10 12:09:50][main:177]   1. Not running as administrator
[2025-07-10 12:09:50][main:178]   2. WinDivert driver not installed
[2025-07-10 12:09:50][main:179]   3. Invalid filter syntax: tcp && ProcessName == firefox.exe
[2025-07-10 12:09:50][main:180]   4. Target process not found
```

## 测试步骤

### 1. 准备环境

1. **启动mitmproxy**
   ```bash
   mitmproxy -p 8080 --set confdir=~/.mitmproxy
   ```

2. **以管理员身份运行代理工具**
   ```bash
   # 右键点击命令提示符，选择"以管理员身份运行"
   process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == firefox.exe"
   ```

### 2. 测试流量拦截

1. **启动目标浏览器**
   - 启动Firefox浏览器

2. **访问网站**
   - 访问任意HTTP/HTTPS网站
   - 观察代理工具的日志输出

3. **验证拦截**
   - 在mitmproxy界面中应该能看到拦截的请求
   - 代理工具应该显示重定向的数据包

### 3. 常见测试场景

```bash
# 测试Firefox
process-proxy.exe -v -s -f "tcp && ProcessName == firefox.exe"

# 测试Chrome
process-proxy.exe -v -s -f "tcp && ProcessName == chrome.exe"

# 测试Edge
process-proxy.exe -v -s -f "tcp && ProcessName == msedge.exe"

# 测试特定端口
process-proxy.exe -v -s -p 9090 -f "tcp && ProcessName == chrome.exe"
```

## 故障排除

### 1. 权限问题
```
错误: failed to open WinDivert device (error code: 5)
解决: 以管理员身份运行程序
```

### 2. 驱动问题
```
错误: failed to open WinDivert device (error code: 2)
解决: 确保WinDivert64.sys文件存在且未被杀毒软件阻止
```

### 3. 进程名问题
```
现象: 没有拦截到任何数据包
解决: 
1. 使用任务管理器确认进程名称
2. 注意大小写敏感
3. 确保目标进程正在运行
```

### 4. 端口冲突
```
现象: mitmproxy无法启动
解决: 更换代理端口号，确保端口未被占用
```

## 高级用法

### 1. 自定义过滤器

```bash
# 拦截所有TCP流量（谨慎使用）
process-proxy.exe -v -f "tcp"

# 拦截特定IP的流量
process-proxy.exe -v -f "tcp && ip.DstAddr == *************"

# 拦截多个进程
process-proxy.exe -v -f "tcp && (ProcessName == firefox.exe || ProcessName == chrome.exe)"
```

### 2. 性能监控

```bash
# 启用详细统计
process-proxy.exe -v -s -f "tcp && ProcessName == chrome.exe"
```

### 3. 调试模式

```bash
# 最详细的输出
process-proxy.exe -v -s -p 8080 -f "tcp && ProcessName == target.exe"
```

## 注意事项

1. **管理员权限**: 必须以管理员身份运行
2. **杀毒软件**: 可能需要将程序添加到白名单
3. **防火墙**: 确保代理端口未被防火墙阻止
4. **进程名**: 使用准确的进程名称（区分大小写）
5. **性能影响**: 详细模式会产生大量日志，可能影响性能

## 技术细节

- **编译器**: gcc (MinGW-w64)
- **依赖库**: WinDivert 2.2.2-A
- **目标平台**: Windows x64
- **C标准**: C99
- **网络层**: WinDivert网络层拦截
- **协议支持**: TCP (HTTP/HTTPS)

## 更新日志

### v1.0 (当前版本)
- ✅ 基础流量重定向功能
- ✅ 详细日志输出
- ✅ 实时统计信息
- ✅ 错误处理和诊断
- ✅ 命令行参数支持
- ✅ 管理员权限检查
