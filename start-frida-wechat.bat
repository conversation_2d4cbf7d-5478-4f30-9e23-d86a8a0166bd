@echo off
chcp 65001 >nul

echo ========================================
echo 微信Frida流量捕获工具
echo ========================================
echo.

:: 检查Frida是否安装
python -c "import frida" >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] Frida已安装
) else (
    echo [✗] Frida未安装，正在安装...
    pip install frida-tools
    if %errorLevel% == 0 (
        echo [✓] Frida安装成功
    ) else (
        echo [✗] Frida安装失败，请手动安装: pip install frida-tools
        pause
        exit /b 1
    )
)

:: 检查微信进程
echo.
echo 正在查找微信进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq WeChat.exe" /fo csv ^| find "WeChat.exe"') do (
    set WECHAT_PID=%%i
    set WECHAT_PID=!WECHAT_PID:"=!
)

if defined WECHAT_PID (
    echo [✓] 找到微信进程，PID: %WECHAT_PID%
) else (
    echo [!] 未找到微信进程
    echo 请先启动微信，然后重新运行此脚本
    set /p start_wechat="是否现在启动微信？(y/n): "
    if /i "!start_wechat!"=="y" (
        echo 正在启动微信...
        start "" "C:\Program Files\Tencent\WeChat\WeChat.exe" 2>nul
        if errorlevel 1 (
            start "" "C:\Program Files (x86)\Tencent\WeChat\WeChat.exe" 2>nul
        )
        echo 请等待微信启动完成，然后重新运行此脚本
    )
    pause
    exit /b 1
)

echo.
echo 可用的Hook脚本:
echo 1. 基础网络流量捕获 (wechat_network_hook.js)
echo 2. 高级流量分析 (wechat_advanced_hook.js)
echo 3. 自定义脚本路径
echo 4. 退出
echo.
set /p script_choice="请选择脚本 (1-4): "

if "%script_choice%"=="1" (
    set SCRIPT_FILE=wechat_network_hook.js
    echo 使用基础网络流量捕获脚本
) else if "%script_choice%"=="2" (
    set SCRIPT_FILE=wechat_advanced_hook.js
    echo 使用高级流量分析脚本
) else if "%script_choice%"=="3" (
    set /p SCRIPT_FILE="请输入脚本文件路径: "
) else if "%script_choice%"=="4" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择，使用默认脚本
    set SCRIPT_FILE=wechat_network_hook.js
)

:: 检查脚本文件是否存在
if not exist "%SCRIPT_FILE%" (
    echo [✗] 脚本文件不存在: %SCRIPT_FILE%
    echo 请确保脚本文件在当前目录中
    pause
    exit /b 1
)

echo.
echo 配置信息:
echo - 目标进程: WeChat.exe (PID: %WECHAT_PID%)
echo - Hook脚本: %SCRIPT_FILE%
echo - 输出: 控制台 + 日志文件
echo.

echo 重要提醒:
echo 1. 此工具仅用于学习和研究目的
echo 2. 请遵守相关法律法规
echo 3. 不要用于分析他人隐私数据
echo 4. 微信可能检测到hook行为
echo.

set /p confirm="确认开始hook微信？(y/n): "
if /i not "%confirm%"=="y" (
    echo 操作已取消
    exit /b 0
)

echo.
echo 正在启动Frida hook...
echo 按Ctrl+C停止监控
echo ========================================
echo.

:: 启动Frida
frida -p %WECHAT_PID% -l "%SCRIPT_FILE%" --no-pause

echo.
echo Hook已停止
pause
