# Frida微信流量分析完整指南

## 🎯 **方案概述**

使用Frida动态hook微信进程，在运行时拦截网络API调用，获取加密前的原始数据。这是最有效的微信流量分析方法之一。

## 📋 **准备工作**

### 1. 安装依赖

```bash
# 安装Python和pip（如果没有）
# 安装Frida
pip install frida-tools

# 验证安装
frida --version
```

### 2. 准备脚本文件

我们提供了三个专用脚本：

| 脚本文件 | 功能 | 适用场景 |
|----------|------|----------|
| `wechat_network_hook.js` | 基础网络API hook | 通用网络流量捕获 |
| `wechat_advanced_hook.js` | 高级流量分析 | 协议解析和分类 |
| `wechat_ssl_hook.js` | SSL/TLS解密 | HTTPS流量解密 |

## 🚀 **使用方法**

### 方法1: 自动化脚本（推荐）

```bash
# 双击运行或在命令行执行
start-frida-wechat.bat
```

这个脚本会：
- ✅ 自动检测微信进程
- ✅ 选择hook脚本
- ✅ 启动Frida监控

### 方法2: 手动命令

```bash
# 1. 查找微信进程ID
tasklist | findstr WeChat.exe

# 2. 使用基础hook脚本
frida -p <PID> -l wechat_network_hook.js

# 3. 使用高级分析脚本
frida -p <PID> -l wechat_advanced_hook.js

# 4. 使用SSL解密脚本
frida -p <PID> -l wechat_ssl_hook.js
```

## 📊 **脚本功能详解**

### 基础网络Hook (`wechat_network_hook.js`)

**监控的API:**
- `send()` - 发送数据
- `recv()` - 接收数据  
- `connect()` - 建立连接
- `WinHttpSendRequest()` - HTTP请求
- `SSL_write()/SSL_read()` - SSL加密数据

**输出示例:**
```json
{
  "timestamp": "2025-07-10T12:45:30.123Z",
  "direction": "SEND",
  "socket": "1234",
  "length": 256,
  "hex": "47 45 54 20 2f 61 70 69...",
  "ascii": "GET /api/message..."
}
```

### 高级流量分析 (`wechat_advanced_hook.js`)

**额外功能:**
- ✅ 数据类型检测（HTTP/JSON/XML/Binary）
- ✅ HTTP请求/响应解析
- ✅ 微信协议特征识别
- ✅ 流量统计和分类

**输出示例:**
```json
{
  "direction": "SEND",
  "type": "HTTP",
  "http": {
    "method": "POST",
    "url": "/cgi-bin/mmwebwx-bin/webwxsendmsg",
    "headers": {
      "Content-Type": "application/json",
      "User-Agent": "Mozilla/5.0..."
    },
    "body": "{\"BaseRequest\":{...}}"
  }
}
```

### SSL解密Hook (`wechat_ssl_hook.js`)

**专门功能:**
- ✅ SSL/TLS加密前数据捕获
- ✅ HTTPS请求/响应解密
- ✅ SSL会话管理
- ✅ 证书信息提取

## 🔍 **分析技巧**

### 1. 过滤特定流量

在脚本中添加过滤条件：

```javascript
// 只记录包含特定关键词的数据
if (data.preview.includes("webwxsendmsg") || 
    data.preview.includes("webwxgetmsg")) {
    logTraffic("WECHAT_API", data);
}
```

### 2. 保存数据到文件

```javascript
// 在脚本中添加文件写入功能
var fs = require('fs');
fs.appendFileSync('wechat_traffic.log', JSON.stringify(data) + '\n');
```

### 3. 实时数据分析

```javascript
// 检测消息发送
if (data.type === "HTTP" && data.http.url.includes("sendmsg")) {
    console.log("[消息发送] " + data.http.body);
}

// 检测朋友圈更新
if (data.preview.includes("timeline")) {
    console.log("[朋友圈活动] 检测到朋友圈相关流量");
}
```

## 🎯 **常见微信API端点**

通过Frida可以捕获到的微信API：

| API端点 | 功能 | 数据类型 |
|---------|------|----------|
| `/webwxsendmsg` | 发送消息 | JSON |
| `/webwxgetmsg` | 获取消息 | JSON |
| `/webwxgetcontact` | 获取联系人 | JSON |
| `/webwxuploadmedia` | 上传媒体 | Binary |
| `/webwxgetmsgimg` | 获取图片 | Binary |
| `/webwxgetvideo` | 获取视频 | Binary |

## ⚠️ **注意事项**

### 技术限制
- ❌ 微信可能检测到Frida并退出
- ❌ 某些版本的微信使用了反调试技术
- ❌ SSL证书绑定可能阻止解密

### 绕过检测
```javascript
// 在脚本开头添加反检测代码
Java.perform(function() {
    // 禁用调试检测
    var Debug = Java.use("android.os.Debug");
    Debug.isDebuggerConnected.implementation = function() {
        return false;
    };
});
```

### 法律和道德
- ✅ 仅用于学习和研究
- ✅ 不要分析他人隐私数据
- ✅ 遵守相关法律法规

## 🛠️ **故障排除**

### 1. Frida连接失败
```bash
# 检查进程是否存在
tasklist | findstr WeChat.exe

# 尝试以管理员身份运行
# 检查防火墙设置
```

### 2. Hook失败
```bash
# 检查API是否存在
frida -p <PID> -e "console.log(Module.findExportByName('ws2_32.dll', 'send'))"

# 尝试不同的库名
```

### 3. 数据解析错误
```javascript
// 添加错误处理
try {
    var data = Memory.readByteArray(buffer, length);
    // 处理数据
} catch (e) {
    console.log("数据读取失败:", e);
}
```

## 📈 **高级用法**

### 1. 结合mitmproxy
```bash
# 同时使用Frida和mitmproxy
# Frida捕获加密前数据
# mitmproxy捕获网络层数据
```

### 2. 自动化分析
```python
# Python脚本自动分析Frida输出
import json
import re

def analyze_wechat_traffic(log_file):
    with open(log_file, 'r') as f:
        for line in f:
            data = json.loads(line)
            if 'sendmsg' in data.get('preview', ''):
                print(f"发现消息: {data}")
```

### 3. 实时监控
```javascript
// 实时统计和报警
var messageCount = 0;
setInterval(function() {
    console.log(`消息统计: ${messageCount} 条/分钟`);
    messageCount = 0;
}, 60000);
```

## 🚀 **快速开始**

1. **安装Frida**: `pip install frida-tools`
2. **启动微信**
3. **运行脚本**: `start-frida-wechat.bat`
4. **选择hook模式**
5. **在微信中进行操作**
6. **观察输出数据**

这个Frida方案可以有效捕获微信的网络流量，包括加密前的原始数据！
