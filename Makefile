# Makefile for Process Proxy Tool
# 使用本机安装的gcc编译

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -O2 -std=c99
LDFLAGS = -lws2_32

# 目标文件
TARGET = process-proxy.exe
SOURCE = main.c

# WinDivert库路径（需要根据实际安装路径调整）
WINDIVERT_PATH = ./WinDivert
WINDIVERT_LIB = $(WINDIVERT_PATH)/WinDivert.lib
WINDIVERT_DLL = $(WINDIVERT_PATH)/WinDivert.dll
WINDIVERT_SYS = $(WINDIVERT_PATH)/WinDivert64.sys

# 包含路径
INCLUDES = -I$(WINDIVERT_PATH)

# 默认目标
all: $(TARGET)

# 编译主程序
$(TARGET): $(SOURCE)
	@echo "正在编译 $(TARGET)..."
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET) $(SOURCE) $(WINDIVERT_LIB) $(LDFLAGS)
	@echo "编译完成！"

# 检查WinDivert依赖
check-deps:
	@echo "检查WinDivert依赖..."
	@if [ ! -f "$(WINDIVERT_LIB)" ]; then \
		echo "错误: 找不到 WinDivert.lib，请确保WinDivert已正确安装"; \
		echo "预期路径: $(WINDIVERT_LIB)"; \
		exit 1; \
	fi
	@if [ ! -f "windivert.h" ] && [ ! -f "$(WINDIVERT_PATH)/windivert.h" ]; then \
		echo "错误: 找不到 windivert.h 头文件"; \
		echo "请确保头文件在当前目录或 $(WINDIVERT_PATH) 目录中"; \
		exit 1; \
	fi
	@echo "依赖检查通过！"

# 安装（复制必要的DLL和SYS文件）
install: $(TARGET)
	@echo "安装程序文件..."
	@if [ -f "$(WINDIVERT_DLL)" ]; then \
		cp "$(WINDIVERT_DLL)" .; \
		echo "已复制 WinDivert.dll"; \
	fi
	@if [ -f "$(WINDIVERT_SYS)" ]; then \
		cp "$(WINDIVERT_SYS)" .; \
		echo "已复制 WinDivert64.sys"; \
	fi
	@echo "安装完成！"

# 清理编译文件
clean:
	@echo "清理编译文件..."
	@if [ -f "$(TARGET)" ]; then rm -f "$(TARGET)"; echo "已删除 $(TARGET)"; fi
	@echo "清理完成！"

# 运行测试（需要管理员权限）
test: $(TARGET)
	@echo "运行测试（需要管理员权限）..."
	@echo "使用默认参数启动程序..."
	./$(TARGET) -v -s

# 显示帮助
help:
	@echo "可用的make目标："
	@echo "  all        - 编译程序（默认）"
	@echo "  check-deps - 检查WinDivert依赖"
	@echo "  install    - 安装程序（复制必要文件）"
	@echo "  clean      - 清理编译文件"
	@echo "  test       - 运行测试（需要管理员权限）"
	@echo "  help       - 显示此帮助信息"
	@echo ""
	@echo "编译要求："
	@echo "  - gcc编译器"
	@echo "  - WinDivert库和头文件"
	@echo "  - Windows系统"
	@echo ""
	@echo "使用示例："
	@echo "  make              # 编译程序"
	@echo "  make check-deps   # 检查依赖"
	@echo "  make install      # 安装程序"
	@echo "  make clean        # 清理文件"

# 声明伪目标
.PHONY: all check-deps install clean test help
