@echo off
chcp 65001 >nul
echo ========================================
echo Process Proxy Tool 编译脚本
echo ========================================
echo.

:: 检查是否以管理员身份运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] 检测到管理员权限
) else (
    echo [!] 警告: 未检测到管理员权限，运行时可能需要管理员权限
)
echo.

:: 检查gcc是否安装
echo [1/5] 检查gcc编译器...
gcc --version >nul 2>&1
if %errorLevel% == 0 (
    echo [✓] gcc编译器已安装
    gcc --version | findstr "gcc"
) else (
    echo [✗] 错误: 未找到gcc编译器
    echo 请安装MinGW-w64或其他gcc发行版
    pause
    exit /b 1
)
echo.

:: 检查WinDivert文件
echo [2/5] 检查WinDivert依赖...
set WINDIVERT_PATH=WinDivert-2.2.2-A
set WINDIVERT_INCLUDE=%WINDIVERT_PATH%\include
set WINDIVERT_LIB_PATH=%WINDIVERT_PATH%\x64
set WINDIVERT_DLL_PATH=%WINDIVERT_PATH%\x64

if exist "%WINDIVERT_INCLUDE%\windivert.h" (
    echo [✓] 找到 %WINDIVERT_INCLUDE%\windivert.h
) else if exist "windivert.h" (
    echo [✓] 找到 windivert.h
    set WINDIVERT_INCLUDE=.
) else (
    echo [✗] 错误: 未找到 windivert.h 头文件
    echo 请确保WinDivert头文件存在于以下位置之一:
    echo   - %WINDIVERT_INCLUDE%\windivert.h
    echo   - .\windivert.h
    pause
    exit /b 1
)

if exist "%WINDIVERT_LIB_PATH%\WinDivert.lib" (
    echo [✓] 找到 %WINDIVERT_LIB_PATH%\WinDivert.lib
    set WINDIVERT_LIB=%WINDIVERT_LIB_PATH%\WinDivert.lib
) else if exist "WinDivert.lib" (
    echo [✓] 找到 WinDivert.lib
    set WINDIVERT_LIB=WinDivert.lib
) else (
    echo [✗] 错误: 未找到 WinDivert.lib 库文件
    echo 请确保WinDivert库文件存在于以下位置之一:
    echo   - %WINDIVERT_LIB_PATH%\WinDivert.lib
    echo   - .\WinDivert.lib
    pause
    exit /b 1
)
echo.

:: 清理旧文件
echo [3/5] 清理旧的编译文件...
if exist "process-proxy.exe" (
    del "process-proxy.exe"
    echo [✓] 已删除旧的可执行文件
)
echo.

:: 编译程序
echo [4/5] 编译程序...
echo 编译命令: gcc -Wall -Wextra -O2 -std=c99 -I"%WINDIVERT_INCLUDE%" -o process-proxy.exe main.c "%WINDIVERT_LIB%" -lws2_32

gcc -Wall -Wextra -O2 -std=c99 -I"%WINDIVERT_INCLUDE%" -o process-proxy.exe main.c "%WINDIVERT_LIB%" -lws2_32

if %errorLevel% == 0 (
    echo [✓] 编译成功！
) else (
    echo [✗] 编译失败！
    echo 请检查错误信息并修复代码问题
    pause
    exit /b 1
)
echo.

:: 复制必要的DLL文件
echo [5/5] 复制运行时文件...
if exist "WinDivert.dll" (
    echo [✓] WinDivert.dll 已存在
) else if exist "%WINDIVERT_DLL_PATH%\WinDivert.dll" (
    copy "%WINDIVERT_DLL_PATH%\WinDivert.dll" "."
    echo [✓] 已复制 WinDivert.dll
) else (
    echo [!] 警告: 未找到 WinDivert.dll，运行时可能出错
)

if exist "WinDivert64.sys" (
    echo [✓] WinDivert64.sys 已存在
) else if exist "%WINDIVERT_DLL_PATH%\WinDivert64.sys" (
    copy "%WINDIVERT_DLL_PATH%\WinDivert64.sys" "."
    echo [✓] 已复制 WinDivert64.sys
) else (
    echo [!] 警告: 未找到 WinDivert64.sys，运行时可能出错
)
echo.

:: 显示编译结果
echo ========================================
echo 编译完成！
echo ========================================
echo 生成的文件: process-proxy.exe
if exist "process-proxy.exe" (
    echo 文件大小: 
    dir process-proxy.exe | findstr "process-proxy.exe"
)
echo.
echo 使用方法:
echo   process-proxy.exe -h                    显示帮助
echo   process-proxy.exe -v                    启用详细输出
echo   process-proxy.exe -p 8080               指定代理端口
echo   process-proxy.exe -f "tcp && ProcessName == chrome.exe"  自定义过滤器
echo.
echo 注意: 运行时需要管理员权限！
echo ========================================

pause
