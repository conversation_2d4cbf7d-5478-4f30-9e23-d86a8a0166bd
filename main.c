#include <winsock2.h>
#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "windivert.h"

// 全局变量控制详细输出
static int g_verbose = 0;
static int g_packet_count = 0;

// 获取当前时间字符串
void get_timestamp(char *buffer, size_t size) {
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);
    strftime(buffer, size, "%Y-%m-%d %H:%M:%S", tm_info);
}

// 日志宏定义：打印时间戳、文件名、函数名、行号
#define LOG(fmt, ...) \
    do { \
        char timestamp[32]; \
        get_timestamp(timestamp, sizeof(timestamp)); \
        fprintf(stderr, "[%s][%s:%d] " fmt "\n", timestamp, __FUNCTION__, __LINE__, ##__VA_ARGS__); \
        fflush(stderr); \
    } while (0)

// 详细日志宏（只在verbose模式下输出）
#define VERBOSE_LOG(fmt, ...) \
    do { \
        if (g_verbose) { \
            char timestamp[32]; \
            get_timestamp(timestamp, sizeof(timestamp)); \
            fprintf(stderr, "[%s][VERBOSE][%s:%d] " fmt "\n", timestamp, __FUNCTION__, __LINE__, ##__VA_ARGS__); \
            fflush(stderr); \
        } \
    } while (0)

// 打印IP地址
void print_ip_address(UINT32 ip, char *buffer, size_t size) {
    snprintf(buffer, size, "%d.%d.%d.%d",
             (ip >> 0) & 0xFF,
             (ip >> 8) & 0xFF,
             (ip >> 16) & 0xFF,
             (ip >> 24) & 0xFF);
}

// 打印数据包详细信息
void print_packet_info(PWINDIVERT_IPHDR ip_header, PWINDIVERT_TCPHDR tcp_header, UINT packet_len, const char *action) {
    if (!g_verbose) return;

    char src_ip[16], dst_ip[16];
    print_ip_address(ntohl(ip_header->SrcAddr), src_ip, sizeof(src_ip));
    print_ip_address(ntohl(ip_header->DstAddr), dst_ip, sizeof(dst_ip));

    VERBOSE_LOG("Packet #%d [%s]: %s:%d -> %s:%d (len=%d, proto=%d)",
                ++g_packet_count, action,
                src_ip, ntohs(tcp_header->SrcPort),
                dst_ip, ntohs(tcp_header->DstPort),
                packet_len, ip_header->Protocol);
}

// 显示帮助信息
void show_help() {
    printf("Usage: process-proxy [OPTIONS]\n");
    printf("Proxy tool to redirect traffic through mitmproxy using WinDivert.\n\n");
    printf("Options:\n");
    printf("  -h, --help            Show this help message and exit\n");
    printf("  -p, --port PORT       Specify the proxy port (default: 8080)\n");
    printf("  -t, --target PROCESS  Target process name (default: firefox.exe)\n");
    printf("  -f, --filter FILTER   Set custom WinDivert filter rule\n");
    printf("  -v, --verbose         Enable verbose output with detailed packet info\n");
    printf("  -s, --stats           Show statistics every 10 seconds\n");
    printf("\nDefault filter: \"outbound and tcp.DstPort == 80\"\n");
    printf("Default target: \"firefox.exe\" (for reference only)\n");
    printf("\nExamples:\n");
    printf("  process-proxy -v -p 8888\n");
    printf("  process-proxy -v -p 8888 -f \"outbound and tcp.DstPort == 443\"\n");
    printf("  process-proxy -f \"outbound and (tcp.DstPort == 80 or tcp.DstPort == 443)\"\n");
    printf("\nIMPORTANT: Must run as administrator!\n");
    printf("NOTE: This version intercepts ALL matching traffic, not just specific processes.\n");
}

// 显示统计信息
void show_stats() {
    static int last_packet_count = 0;
    static time_t last_time = 0;

    time_t current_time = time(NULL);
    if (last_time == 0) {
        last_time = current_time;
        return;
    }

    int packets_per_second = (g_packet_count - last_packet_count) / (current_time - last_time);
    LOG("Statistics: Total packets: %d, Rate: %d packets/sec", g_packet_count, packets_per_second);

    last_packet_count = g_packet_count;
    last_time = current_time;
}

int main(int argc, char *argv[]) {
    int port = 8080;
    const char *filter = "outbound and tcp.DstPort == 80";  // 简化：只拦截出站HTTP流量
    const char *target_process = "firefox.exe";  // 单独存储目标进程名
    int show_statistics = 0;

    LOG("Process Proxy Tool v1.0 - Starting initialization...");

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            show_help();
            return 0;
        } else if (strcmp(argv[i], "-p") == 0 || strcmp(argv[i], "--port") == 0) {
            if (i + 1 < argc) {
                port = atoi(argv[++i]);
                if (port <= 0 || port > 65535) {
                    LOG("Error: invalid port number %d (must be 1-65535)", port);
                    return 1;
                }
                LOG("Proxy port set to: %d", port);
            } else {
                LOG("Error: missing argument after -p");
                return 1;
            }
        } else if (strcmp(argv[i], "-f") == 0 || strcmp(argv[i], "--filter") == 0) {
            if (i + 1 < argc) {
                filter = argv[++i];
                LOG("Custom filter set to: %s", filter);
            } else {
                LOG("Error: missing argument after -f");
                return 1;
            }
        } else if (strcmp(argv[i], "-t") == 0 || strcmp(argv[i], "--target") == 0) {
            if (i + 1 < argc) {
                target_process = argv[++i];
                LOG("Target process set to: %s", target_process);
            } else {
                LOG("Error: missing argument after -t");
                return 1;
            }
        } else if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--verbose") == 0) {
            g_verbose = 1;
            LOG("Verbose mode enabled");
        } else if (strcmp(argv[i], "-s") == 0 || strcmp(argv[i], "--stats") == 0) {
            show_statistics = 1;
            LOG("Statistics mode enabled");
        } else {
            LOG("Unknown option: %s", argv[i]);
            show_help();
            return 1;
        }
    }

    LOG("Configuration complete:");
    LOG("  - Proxy port: %d", port);
    LOG("  - Filter rule: %s", filter);
    LOG("  - Target process: %s", target_process);
    LOG("  - Verbose mode: %s", g_verbose ? "enabled" : "disabled");
    LOG("  - Statistics: %s", show_statistics ? "enabled" : "disabled");

    HANDLE handle;
    WINDIVERT_ADDRESS addr;
    UINT packet_len;
    UCHAR packet[UINT16_MAX];
    PWINDIVERT_IPHDR ip_header;
    PWINDIVERT_TCPHDR tcp_header;
    time_t last_stats_time = time(NULL);
    int redirected_packets = 0;
    int total_packets = 0;

    // 检查管理员权限
    LOG("Checking administrator privileges...");
    BOOL is_admin = FALSE;
    HANDLE token = NULL;
    if (OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &token)) {
        TOKEN_ELEVATION elevation;
        DWORD size = sizeof(TOKEN_ELEVATION);
        if (GetTokenInformation(token, TokenElevation, &elevation, sizeof(elevation), &size)) {
            is_admin = elevation.TokenIsElevated;
        }
        CloseHandle(token);
    }

    if (!is_admin) {
        LOG("Warning: Not running as administrator. WinDivert may fail to open.");
    } else {
        LOG("Running with administrator privileges.");
    }

    // 打开 WinDivert 句柄
    LOG("Opening WinDivert device with filter: %s", filter);
    LOG("Target process: %s (process filtering will be done in software)", target_process);
    handle = WinDivertOpen(filter, WINDIVERT_LAYER_NETWORK, 0, 0);
    if (handle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        LOG("Error: failed to open WinDivert device (error code: %lu)", error);
        LOG("Possible causes:");
        LOG("  1. Not running as administrator");
        LOG("  2. WinDivert driver not installed");
        LOG("  3. Invalid filter syntax: %s", filter);
        LOG("  4. Network interface not available");
        return 1;
    }
    LOG("WinDivert device opened successfully.");

    // 设置接收超时（可选，用于定期显示统计信息）
    if (show_statistics) {
        if (!WinDivertSetParam(handle, WINDIVERT_PARAM_QUEUE_TIME, 1000)) {
            LOG("Warning: failed to set queue timeout");
        }
    }

    LOG("Entering main packet processing loop...");
    LOG("Monitoring traffic and redirecting HTTP(80)/HTTPS(443) to proxy port %d", port);

    while (TRUE)
    {
        // 定期显示统计信息
        if (show_statistics) {
            time_t current_time = time(NULL);
            if (current_time - last_stats_time >= 10) {
                LOG("Statistics: Total=%d, Redirected=%d, Rate=%.1f pkt/s",
                    total_packets, redirected_packets,
                    (double)total_packets / (current_time - (last_stats_time - 10)));
                last_stats_time = current_time;
            }
        }

        if (!WinDivertRecv(handle, packet, sizeof(packet), &packet_len, &addr))
        {
            DWORD error = GetLastError();
            if (error == ERROR_TIMEOUT) {
                VERBOSE_LOG("Receive timeout (no packets)");
                continue;
            }
            LOG("Error receiving packet (error code: %lu)", error);
            continue;
        }

        total_packets++;
        VERBOSE_LOG("Received packet #%d (length: %d bytes)", total_packets, packet_len);

        // 解析IP头
        ip_header = (PWINDIVERT_IPHDR)packet;
        if (ip_header->Protocol != IPPROTO_TCP)
        {
            VERBOSE_LOG("Non-TCP packet (protocol: %d), forwarding unchanged", ip_header->Protocol);
            WinDivertSend(handle, packet, packet_len, NULL, &addr);
            continue;
        }

        // 解析TCP头
        tcp_header = (PWINDIVERT_TCPHDR)(packet + ip_header->HdrLength * 4);
        UINT16 dst_port = ntohs(tcp_header->DstPort);
        // UINT16 src_port = ntohs(tcp_header->SrcPort);  // 暂时不使用，避免警告

        // 打印数据包信息（详细模式）
        print_packet_info(ip_header, tcp_header, packet_len, "RECEIVED");

        // 检查是否需要重定向
        if (dst_port == 80 || dst_port == 443)
        {
            VERBOSE_LOG("Redirecting %s traffic from port %d to proxy port %d",
                       (dst_port == 80) ? "HTTP" : "HTTPS", dst_port, port);

            // 修改目标端口
            tcp_header->DstPort = htons(port);

            // 重新计算校验和
            ip_header->Checksum = 0;
            tcp_header->Checksum = 0;

            // 使用WinDivert重新计算校验和
            WinDivertHelperCalcChecksums(packet, packet_len, &addr, 0);

            redirected_packets++;
            print_packet_info(ip_header, tcp_header, packet_len, "REDIRECTED");

            if (!WinDivertSend(handle, packet, packet_len, NULL, &addr)) {
                DWORD error = GetLastError();
                LOG("Error sending redirected packet (error code: %lu)", error);
            } else {
                VERBOSE_LOG("Redirected packet sent successfully");
            }
        }
        else
        {
            VERBOSE_LOG("Port %d not targeted for redirection, forwarding unchanged", dst_port);
            print_packet_info(ip_header, tcp_header, packet_len, "FORWARDED");

            if (!WinDivertSend(handle, packet, packet_len, NULL, &addr)) {
                DWORD error = GetLastError();
                LOG("Error sending original packet (error code: %lu)", error);
            }
        }
    }

    LOG("Closing WinDivert handle...");
    WinDivertClose(handle);
    LOG("Final statistics: Total packets: %d, Redirected packets: %d", total_packets, redirected_packets);
    return 0;
}