#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include "windivert.h"

// 日志宏定义：打印文件名、函数名、行号
#define LOG(fmt, ...) \
    do { \
        fprintf(stderr, "[%s:%d] " fmt "\n", __FUNCTION__, __LINE__, ##__VA_ARGS__); \
    } while (0)

// 显示帮助信息
void show_help() {
    printf("Usage: process-proxy [OPTIONS]\n");
    printf("Proxy tool to redirect traffic through mitmproxy using WinDivert.\n\n");
    printf("Options:\n");
    printf("  -h, --help\t\tShow this help message and exit\n");
    printf("  -p, --port PORT\tSpecify the proxy port (default: 8080)\n");
    printf("  -f, --filter FILTER\tSet custom WinDivert filter rule\n");
    printf("  -v, --verbose\t\tEnable verbose output\n");
}

int main(int argc, char *argv[]) {
    int port = 8080;
    const char *filter = "tcp && ProcessName == firefox.exe";
    int verbose = 0;

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--help") == 0) {
            show_help();
            return 0;
        } else if (strcmp(argv[i], "-p") == 0 || strcmp(argv[i], "--port") == 0) {
            if (i + 1 < argc) {
                port = atoi(argv[++i]);
            } else {
                LOG("Error: missing argument after -p");
                return 1;
            }
        } else if (strcmp(argv[i], "-f") == 0 || strcmp(argv[i], "--filter") == 0) {
            if (i + 1 < argc) {
                filter = argv[++i];
            } else {
                LOG("Error: missing argument after -f");
                return 1;
            }
        } else if (strcmp(argv[i], "-v") == 0 || strcmp(argv[i], "--verbose") == 0) {
            verbose = 1;
        } else {
            LOG("Unknown option: %s", argv[i]);
            return 1;
        }
    }

    LOG("Starting proxy with filter: %s on port %d", filter, port);

    HANDLE handle;
    WINDIVERT_ADDRESS addr;
    UINT packet_len;
    UCHAR packet[UINT16_MAX];
    PWINDIVERT_IPHDR ip_header;
    PWINDIVERT_TCPHDR tcp_header;

    // 打开 WinDivert 句柄
    LOG("Opening WinDivert device with filter: %s", filter);
    handle = WinDivertOpen(filter, WINDIVERT_LAYER_NETWORK, 0, 0);
    if (handle == INVALID_HANDLE_VALUE) {
        LOG("Error: failed to open WinDivert device. Is the filter correct and are you running as admin?");
        return 1;
    }
    LOG("WinDivert device opened successfully.");

    LOG("Entering main loop...");
    while (TRUE)
    {
        if (!WinDivertRecv(handle, packet, sizeof(packet), &packet_len, &addr))
        {
            LOG("Error receiving packet.");
            continue;
        }

        ip_header = (PWINDIVERT_IPHDR)packet;
        if (ip_header->Protocol != IPPROTO_TCP)
        {
            WinDivertSend(handle, packet, packet_len, NULL, &addr);
            continue;
        }

        tcp_header = (PWINDIVERT_TCPHDR)(packet + ip_header->HdrLength);
        if (ntohs(tcp_header->DstPort) == 80 || ntohs(tcp_header->DstPort) == 443)
        {
            tcp_header->DstPort = htons(port);
            ip_header->Checksum = 0;
            tcp_header->Checksum = 0;

            WinDivertSend(handle, packet, packet_len, NULL, &addr);
        }
        else
        {
            WinDivertSend(handle, packet, packet_len, NULL, &addr);
        }
    }

    WinDivertClose(handle);
    return 0;
}