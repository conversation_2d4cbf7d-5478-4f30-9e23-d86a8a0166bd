# Firefox Proxy Tool PowerShell Script
# Run with: powershell -ExecutionPolicy Bypass -File Start-FirefoxProxy.ps1

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Firefox Traffic Proxy Tool" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "[ERROR] This script must be run as Administrator" -ForegroundColor Red
    Write-Host "Please right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[OK] Running with administrator privileges" -ForegroundColor Green
Write-Host ""

# Check if process-proxy.exe exists
if (-not (Test-Path "process-proxy.exe")) {
    Write-Host "[ERROR] process-proxy.exe not found" -ForegroundColor Red
    Write-Host "Please run compile.bat first to build the program" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[OK] Found process-proxy.exe" -ForegroundColor Green
Write-Host ""

# Display configuration
Write-Host "Configuration:" -ForegroundColor Yellow
Write-Host "  Target Process: Firefox (firefox.exe)"
Write-Host "  Proxy Port: 8888"
Write-Host "  Verbose Output: Enabled"
Write-Host "  Statistics: Enabled"
Write-Host ""

# Display instructions
Write-Host "Instructions:" -ForegroundColor Yellow
Write-Host "  1. Make sure mitmproxy is running: mitmproxy -p 8888"
Write-Host "  2. Start Firefox browser"
Write-Host "  3. Visit any HTTP/HTTPS website in Firefox"
Write-Host "  4. Watch the log output below"
Write-Host "  5. Press Ctrl+C to stop the proxy"
Write-Host ""

Write-Host "Starting proxy tool..." -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Start the proxy tool
try {
    & ".\process-proxy.exe" -v -s -p 8888 -f "tcp && ProcessName == firefox.exe"
} catch {
    Write-Host ""
    Write-Host "[ERROR] Failed to start proxy tool: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Proxy tool stopped" -ForegroundColor Yellow
Read-Host "Press Enter to exit"
